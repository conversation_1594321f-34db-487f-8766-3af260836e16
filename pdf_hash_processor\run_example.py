#!/usr/bin/env python3
"""
Example script showing how to run the PDF hash processor
"""

import subprocess
import sys
import os

def run_command(command, description):
    """Run a command and display results"""
    print(f"\n{'='*50}")
    print(f"Running: {description}")
    print(f"Command: {command}")
    print('='*50)
    
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        
        if result.stdout:
            print("Output:")
            print(result.stdout)
        
        if result.stderr:
            print("Errors:")
            print(result.stderr)
        
        if result.returncode == 0:
            print(f"✅ {description} completed successfully")
        else:
            print(f"❌ {description} failed with return code {result.returncode}")
            
        return result.returncode == 0
        
    except Exception as e:
        print(f"❌ Error running {description}: {e}")
        return False

def main():
    """Run example commands"""
    print("PDF Hash Processor - Example Usage")
    print("This script demonstrates how to use the PDF hash processor")
    
    # Change to the script directory
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    
    # Step 1: Test the setup
    print("\n🔍 Step 1: Testing the setup...")
    if not run_command("python test_pdf_processor.py", "Database connectivity test"):
        print("❌ Setup test failed. Please check your configuration.")
        return
    
    # Step 2: Show help
    print("\n📖 Step 2: Showing available options...")
    run_command("python pdf_hash_standalone.py --help", "Display help")
    
    # Step 3: Example runs (commented out to avoid accidental execution)
    print("\n📝 Step 3: Example commands (not executed):")
    print("To run the processor, use one of these commands:")
    print("  python pdf_hash_standalone.py --table bse --limit 10")
    print("  python pdf_hash_standalone.py --table nse --limit 10") 
    print("  python pdf_hash_standalone.py --table all --limit 25")
    
    print("\n⚠️  Note: The actual processing commands are not executed in this example.")
    print("   Run them manually when you're ready to process the data.")
    
    print("\n✅ Example completed!")

if __name__ == "__main__":
    main()
