#!/usr/bin/env python3
"""
Test script for PDF Hash Processor
Tests the functionality without making actual database updates.
"""

import requests
import hashlib
import json
from datetime import datetime
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Supabase configuration
SUPABASE_URL = "https://yvuwseolouiqhoxsieop.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl2dXdzZW9sb3VpcWhveHNpZW9wIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI1OTI2ODcsImV4cCI6MjA2ODE2ODY4N30.W7m701xSGJ5eh8oc4turmb4zO9-nz1Pbzqz-DL9EEow"


def test_database_connection():
    """Test connection to Supabase database"""
    try:
        headers = {
            'apikey': SUPABASE_KEY,
            'Authorization': f'Bearer {SUPABASE_KEY}',
            'Content-Type': 'application/json'
        }
        
        # Test BSE table
        url = f"{SUPABASE_URL}/rest/v1/bse_corporate_announcements"
        params = {'select': 'id,attachmentfile,pdf_hash', 'limit': '1'}
        
        response = requests.get(url, headers=headers, params=params, timeout=10)
        response.raise_for_status()
        
        bse_data = response.json()
        logger.info(f"BSE table connection successful. Sample record count: {len(bse_data)}")
        
        # Test NSE table
        url = f"{SUPABASE_URL}/rest/v1/nse_corporate_announcements"
        params = {'select': 'id,attachment_url,pdf_hash', 'limit': '1'}
        
        response = requests.get(url, headers=headers, params=params, timeout=10)
        response.raise_for_status()
        
        nse_data = response.json()
        logger.info(f"NSE table connection successful. Sample record count: {len(nse_data)}")
        
        return True
        
    except Exception as e:
        logger.error(f"Database connection test failed: {e}")
        return False


def test_pdf_download():
    """Test PDF download functionality"""
    # Test with a sample PDF URL (you can replace with actual URL from your database)
    test_urls = [
        "https://www.bseindia.com/xml-data/corpfiling/AttachLive/c4c5e4c5-8b2a-4f3a-9d1e-2f3a4b5c6d7e.pdf",
        "https://www.nseindia.com/content/corporate/eq_ca_sample.pdf"
    ]
    
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    })
    
    for url in test_urls:
        try:
            logger.info(f"Testing download from: {url}")
            response = session.get(url, timeout=10)
            
            if response.status_code == 200:
                content = response.content
                if content.startswith(b'%PDF'):
                    hash_value = hashlib.sha256(content).hexdigest()
                    logger.info(f"Successfully downloaded and hashed PDF: {hash_value[:16]}...")
                else:
                    logger.warning(f"Downloaded content may not be a valid PDF")
            else:
                logger.warning(f"Download failed with status: {response.status_code}")
                
        except Exception as e:
            logger.error(f"Error testing download from {url}: {e}")


def get_sample_records():
    """Get sample records from both tables for testing"""
    try:
        headers = {
            'apikey': SUPABASE_KEY,
            'Authorization': f'Bearer {SUPABASE_KEY}',
            'Content-Type': 'application/json'
        }
        
        # Get BSE records with attachment URLs
        logger.info("Fetching sample BSE records...")
        url = f"{SUPABASE_URL}/rest/v1/bse_corporate_announcements"
        params = {
            'select': 'id,attachmentfile,pdf_hash,duplicate_check_status',
            'attachmentfile': 'not.is.null',
            'limit': '5'
        }
        
        response = requests.get(url, headers=headers, params=params, timeout=10)
        response.raise_for_status()
        bse_records = response.json()
        
        logger.info(f"Found {len(bse_records)} BSE records with attachments")
        for record in bse_records:
            logger.info(f"  ID: {record['id']}, Has PDF Hash: {record.get('pdf_hash') is not None}")
        
        # Get NSE records with attachment URLs
        logger.info("Fetching sample NSE records...")
        url = f"{SUPABASE_URL}/rest/v1/nse_corporate_announcements"
        params = {
            'select': 'id,attachment_url,pdf_hash,duplicate_check_status',
            'attachment_url': 'not.is.null',
            'limit': '5'
        }
        
        response = requests.get(url, headers=headers, params=params, timeout=10)
        response.raise_for_status()
        nse_records = response.json()
        
        logger.info(f"Found {len(nse_records)} NSE records with attachments")
        for record in nse_records:
            logger.info(f"  ID: {record['id']}, Has PDF Hash: {record.get('pdf_hash') is not None}")
        
        return bse_records, nse_records
        
    except Exception as e:
        logger.error(f"Error fetching sample records: {e}")
        return [], []


def test_hash_calculation():
    """Test hash calculation with sample data"""
    # Test with sample PDF content
    sample_content = b'%PDF-1.4\n1 0 obj\n<<\n/Type /Catalog\n/Pages 2 0 R\n>>\nendobj\n'
    
    hash_value = hashlib.sha256(sample_content).hexdigest()
    logger.info(f"Sample hash calculation successful: {hash_value}")
    
    # Test with different content to ensure different hashes
    different_content = b'%PDF-1.4\n1 0 obj\n<<\n/Type /Catalog\n/Pages 3 0 R\n>>\nendobj\n'
    different_hash = hashlib.sha256(different_content).hexdigest()
    
    if hash_value != different_hash:
        logger.info("Hash calculation produces different hashes for different content ✓")
    else:
        logger.error("Hash calculation error: same hash for different content")


def main():
    """Run all tests"""
    logger.info("Starting PDF Hash Processor Tests")
    logger.info("=" * 50)
    
    # Test 1: Database connection
    logger.info("Test 1: Database Connection")
    if test_database_connection():
        logger.info("✓ Database connection test passed")
    else:
        logger.error("✗ Database connection test failed")
        return
    
    print()
    
    # Test 2: Hash calculation
    logger.info("Test 2: Hash Calculation")
    test_hash_calculation()
    logger.info("✓ Hash calculation test completed")
    
    print()
    
    # Test 3: Sample records
    logger.info("Test 3: Sample Records Retrieval")
    bse_records, nse_records = get_sample_records()
    logger.info("✓ Sample records retrieval completed")
    
    print()
    
    # Test 4: PDF download (optional - may fail if URLs are not accessible)
    logger.info("Test 4: PDF Download (Optional)")
    test_pdf_download()
    logger.info("✓ PDF download test completed")
    
    print()
    logger.info("=" * 50)
    logger.info("All tests completed!")
    
    # Summary
    print("\nSummary:")
    print("- The PDF hash processor is ready to use")
    print("- Run 'python pdf_hash_standalone.py --help' for usage options")
    print("- Use '--table bse' or '--table nse' to process specific tables")
    print("- Use '--limit N' to control batch size")


if __name__ == "__main__":
    main()
