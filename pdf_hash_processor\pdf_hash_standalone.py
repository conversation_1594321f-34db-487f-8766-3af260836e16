#!/usr/bin/env python3
"""
Standalone PDF Hash Processor for Corporate Announcements
Downloads PDFs from attachment URLs, calculates hashes, and updates duplicate status.

This script can process both BSE and NSE corporate announcements tables:
- bse_corporate_announcements (attachmentfile column)
- nse_corporate_announcements (attachment_url column)

Usage:
    # Batch processing
    python pdf_hash_standalone.py --table bse  # Process BSE table only
    python pdf_hash_standalone.py --table nse  # Process NSE table only
    python pdf_hash_standalone.py --table all  # Process both tables (default)
    python pdf_hash_standalone.py --limit 10   # Process only 10 records per batch

    # Single record processing
    python pdf_hash_standalone.py --single bse 12345        # Process specific BSE record
    python pdf_hash_standalone.py --single nse abcd-1234    # Process specific NSE record
"""

import requests
import hashlib
import json
import argparse
import time
from datetime import datetime, timezone
from typing import Optional, Dict, List
import logging

# Supabase configuration - Update these with your actual values
SUPABASE_URL = "https://yvuwseolouiqhoxsieop.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl2dXdzZW9sb3VpcWhveHNpZW9wIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI1OTI2ODcsImV4cCI6MjA2ODE2ODY4N30.W7m701xSGJ5eh8oc4turmb4zO9-nz1Pbzqz-DL9EEow"

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class PDFHashProcessor:
    """Standalone PDF hash processor for corporate announcements"""
    
    def __init__(self):
        """Initialize the processor"""
        self.headers = {
            'apikey': SUPABASE_KEY,
            'Authorization': f'Bearer {SUPABASE_KEY}',
            'Content-Type': 'application/json',
            'Prefer': 'return=minimal'
        }
        
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })

        # Configure session for better reliability
        from requests.adapters import HTTPAdapter
        from urllib3.util.retry import Retry

        retry_strategy = Retry(
            total=3,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
            allowed_methods=["HEAD", "GET", "OPTIONS", "PATCH", "POST"]
        )

        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)
        
        # Table configurations
        self.tables = {
            'bse': {
                'name': 'bse_corporate_announcements',
                'url_column': 'attachmentfile'
            },
            'nse': {
                'name': 'nse_corporate_announcements', 
                'url_column': 'attachment_url'
            }
        }
        
        self.stats = {
            'processed': 0,
            'downloaded': 0,
            'duplicates': 0,
            'errors': 0,
            'skipped_non_pdf': 0,
            'skipped_invalid_url': 0,
            'not_found_404': 0,
            'network_errors': 0,
            'other_http_errors': 0
        }
    
    def download_pdf(self, url: str) -> Optional[tuple]:
        """Download PDF content from URL and validate it's a PDF

        Returns:
            tuple: (content: bytes, error_type: str) or (None, error_type)
            error_type can be: 'success', 'invalid_url', 'not_found', 'non_pdf', 'network_error', 'other_error'
        """
        try:
            if not url or not url.strip():
                return None, 'invalid_url'

            url = url.strip()
            if not url.startswith(('http://', 'https://')):
                logger.warning(f"Invalid URL format: {url}")
                self.stats['skipped_invalid_url'] += 1
                return None, 'invalid_url'

            logger.info(f"Downloading: {url}")
            response = self.session.get(url, timeout=30)

            # Handle different HTTP status codes
            if response.status_code == 404:
                logger.warning(f"File not found (404): {url}")
                self.stats['not_found_404'] += 1
                return None, 'not_found'
            elif response.status_code != 200:
                logger.warning(f"HTTP {response.status_code} error for: {url}")
                self.stats['other_http_errors'] += 1
                return None, 'other_http_error'

            content = response.content

            # Check if content is empty
            if not content:
                logger.warning(f"Empty content downloaded from: {url}")
                return None, 'empty_content'

            # Strict PDF validation - must start with PDF header
            if not content.startswith(b'%PDF'):
                logger.warning(f"File is not a valid PDF (missing PDF header): {url}")
                self.stats['skipped_non_pdf'] += 1
                return None, 'non_pdf'

            # Additional PDF validation - check for common PDF structure
            if b'endobj' not in content or b'xref' not in content:
                logger.warning(f"File may be corrupted or not a complete PDF: {url}")
                self.stats['skipped_non_pdf'] += 1
                return None, 'non_pdf'

            # Check content type header if available
            content_type = response.headers.get('content-type', '').lower()
            if content_type and 'pdf' not in content_type:
                logger.warning(f"Content-Type indicates non-PDF file: {content_type} for {url}")
                # Still process if it has PDF header, but log the discrepancy

            logger.info(f"Successfully downloaded valid PDF ({len(content)} bytes)")
            return content, 'success'

        except requests.exceptions.RequestException as e:
            if "404" in str(e):
                logger.warning(f"File not found (404): {url}")
                self.stats['not_found_404'] += 1
                return None, 'not_found'
            elif any(code in str(e) for code in ["timeout", "connection", "network"]):
                logger.warning(f"Network error for {url}: {e}")
                self.stats['network_errors'] += 1
                return None, 'network_error'
            else:
                logger.error(f"HTTP error for {url}: {e}")
                self.stats['other_http_errors'] += 1
                return None, 'other_http_error'
        except Exception as e:
            logger.error(f"Unexpected error downloading {url}: {e}")
            self.stats['errors'] += 1
            return None, 'other_error'
    
    def calculate_hash(self, content: bytes) -> str:
        """Calculate SHA256 hash of content"""
        return hashlib.sha256(content).hexdigest()
    
    def get_records(self, table_config: Dict, limit: int = 100, max_retries: int = 3) -> List[Dict]:
        """Get records that need processing with retry logic"""
        for attempt in range(max_retries):
            try:
                url = f"{SUPABASE_URL}/rest/v1/{table_config['name']}"
                params = {
                    'select': f"id,{table_config['url_column']},pdf_hash,duplicate_check_status",
                    f"{table_config['url_column']}": 'not.is.null',
                    'pdf_hash': 'is.null',
                    'limit': str(limit)
                }

                response = self.session.get(url, headers=self.headers, params=params, timeout=30)
                response.raise_for_status()

                records = response.json()
                logger.info(f"Found {len(records)} records to process")
                return records

            except requests.exceptions.RequestException as e:
                if attempt < max_retries - 1:
                    wait_time = (attempt + 1) * 3  # 3, 6, 9 seconds
                    logger.warning(f"Database query failed (attempt {attempt + 1}/{max_retries}): {e}, retrying in {wait_time}s...")
                    time.sleep(wait_time)
                    continue
                else:
                    logger.error(f"Error fetching records after {max_retries} attempts: {e}")
                    return []
            except Exception as e:
                logger.error(f"Unexpected error fetching records: {e}")
                return []

        return []
    
    def check_duplicate(self, table_name: str, pdf_hash: str, record_id: str) -> bool:
        """Check if hash already exists"""
        try:
            url = f"{SUPABASE_URL}/rest/v1/{table_name}"
            params = {
                'select': 'id',
                'pdf_hash': f'eq.{pdf_hash}',
                'id': f'neq.{record_id}'
            }
            
            response = self.session.get(url, headers=self.headers, params=params)
            response.raise_for_status()
            
            return len(response.json()) > 0
            
        except Exception as e:
            logger.error(f"Error checking duplicates: {e}")
            return False
    
    def update_record(self, table_name: str, record_id: str, pdf_hash: str = None,
                     is_duplicate: bool = False, status: str = 'completed') -> bool:
        """Update record with hash and duplicate status"""
        try:
            # Update fields one by one to avoid trigger issues
            success = True

            # First update the PDF hash if provided
            if pdf_hash:
                success &= self._update_single_field(table_name, record_id, 'pdf_hash', pdf_hash)
                success &= self._update_single_field(table_name, record_id, 'is_duplicate', is_duplicate)

            # Update status
            success &= self._update_single_field(table_name, record_id, 'duplicate_check_status', status)

            # Update timestamp
            success &= self._update_single_field(table_name, record_id, 'updated_at',
                                               datetime.now(timezone.utc).isoformat())

            if success:
                logger.info(f"Updated record {record_id} with status: {status}")
            else:
                logger.error(f"Some updates failed for record {record_id}")

            return success

        except Exception as e:
            logger.error(f"Update failed for {record_id}: {e}")
            return False

    def _update_single_field(self, table_name: str, record_id: str, field_name: str, field_value, max_retries: int = 3) -> bool:
        """Update a single field with retry logic for temporary failures"""
        for attempt in range(max_retries):
            try:
                url = f"{SUPABASE_URL}/rest/v1/{table_name}"
                params = {'id': f'eq.{record_id}'}
                data = {field_name: field_value}

                response = self.session.patch(
                    url, headers=self.headers, params=params, data=json.dumps(data), timeout=30
                )

                if response.status_code not in [200, 204]:
                    # Check for specific error types
                    if response.status_code == 503 or 'upstream connect error' in response.text or 'connection timeout' in response.text:
                        if attempt < max_retries - 1:
                            wait_time = (attempt + 1) * 2  # Exponential backoff: 2, 4, 6 seconds
                            logger.warning(f"503/timeout error updating {field_name} (attempt {attempt + 1}/{max_retries}), retrying in {wait_time}s...")
                            time.sleep(wait_time)
                            continue
                        else:
                            logger.error(f"Failed to update {field_name} after {max_retries} attempts: 503/timeout")
                            return False
                    elif 'master_corporate_announcements' in response.text:
                        logger.warning(f"Master table sync failed for {field_name}, but field may have been updated")
                        # Consider this a success since the main field was likely updated
                        return True
                    else:
                        logger.error(f"Failed to update {field_name}: {response.status_code} - {response.text}")
                        return False

                return True

            except requests.exceptions.RequestException as e:
                if attempt < max_retries - 1:
                    wait_time = (attempt + 1) * 2
                    logger.warning(f"Network error updating {field_name} (attempt {attempt + 1}/{max_retries}): {e}, retrying in {wait_time}s...")
                    time.sleep(wait_time)
                    continue
                else:
                    logger.error(f"Network error updating {field_name} after {max_retries} attempts: {e}")
                    return False
            except Exception as e:
                logger.error(f"Unexpected error updating {field_name} for {record_id}: {e}")
                return False

        return False

    def mark_record_not_found(self, table_name: str, record_id: str) -> bool:
        """Mark record as file not found (404)"""
        return self.update_record(table_name, record_id, status='file_not_found_404')

    def verify_record_update(self, table_name: str, record_id: str) -> Optional[Dict]:
        """Verify that the record was updated correctly"""
        try:
            url = f"{SUPABASE_URL}/rest/v1/{table_name}"
            params = {
                'select': 'id,pdf_hash,is_duplicate,duplicate_check_status,updated_at',
                'id': f'eq.{record_id}'
            }

            response = self.session.get(url, headers=self.headers, params=params)
            response.raise_for_status()

            records = response.json()
            if records:
                return records[0]
            return None

        except Exception as e:
            logger.error(f"Error verifying record update for {record_id}: {e}")
            return None

    def get_single_record(self, exchange: str, record_id: str) -> Optional[Dict]:
        """
        Get a single record by exchange and record ID

        Args:
            exchange (str): 'bse' or 'nse'
            record_id (str): The record ID to fetch

        Returns:
            Optional[Dict]: Record data or None if not found
        """
        try:
            if exchange.lower() not in ['bse', 'nse']:
                logger.error(f"Invalid exchange: {exchange}. Must be 'bse' or 'nse'")
                return None

            table_config = self.tables[exchange.lower()]
            url = f"{SUPABASE_URL}/rest/v1/{table_config['name']}"
            params = {
                'select': f"id,{table_config['url_column']},pdf_hash,duplicate_check_status",
                'id': f'eq.{record_id}'
            }

            response = self.session.get(url, headers=self.headers, params=params)
            response.raise_for_status()

            records = response.json()
            if not records:
                logger.error(f"Record not found: {exchange.upper()} ID {record_id}")
                return None

            return records[0]

        except Exception as e:
            logger.error(f"Error fetching record {record_id} from {exchange}: {e}")
            return None

    def process_single_record_by_id(self, exchange: str, record_id: str) -> bool:
        """
        Process a single record by exchange and record ID

        Args:
            exchange (str): 'bse' or 'nse'
            record_id (str): The record ID to process

        Returns:
            bool: True if processing successful, False otherwise
        """
        try:
            logger.info(f"Processing single record: {exchange.upper()} ID {record_id}")

            # Validate exchange
            if exchange.lower() not in ['bse', 'nse']:
                logger.error(f"Invalid exchange: {exchange}. Must be 'bse' or 'nse'")
                return False

            # Get the record
            record = self.get_single_record(exchange, record_id)
            if not record:
                return False

            # Get table configuration
            table_config = self.tables[exchange.lower()]

            # Check if record already has a hash
            if record.get('pdf_hash'):
                logger.info(f"Record {record_id} already has PDF hash: {record['pdf_hash']}")

                # Check for duplicates with existing hash
                is_duplicate = self.check_duplicate(table_config['name'], record['pdf_hash'], record_id)
                logger.info(f"Duplicate status: {'Yes' if is_duplicate else 'No'}")

                # Update duplicate status if needed
                if record.get('is_duplicate') != is_duplicate:
                    self.update_record(table_config['name'], record_id,
                                     record['pdf_hash'], is_duplicate, 'completed')
                    logger.info(f"Updated duplicate status for record {record_id}")

                return True

            # Process the record normally
            success = self.process_record(table_config, record)

            if success:
                logger.info(f"✅ Successfully processed record {record_id}")
                self.print_single_record_stats(record_id)
            else:
                logger.error(f"❌ Failed to process record {record_id}")

            return success

        except Exception as e:
            logger.error(f"Error processing single record {record_id}: {e}")
            return False

    def print_single_record_stats(self, record_id: str):
        """Print statistics for single record processing"""
        print("\n=== Single Record Processing Results ===")
        print(f"Record ID: {record_id}")
        print(f"✅ Processed: {self.stats['processed']}")
        print(f"📥 Downloaded: {self.stats['downloaded']}")
        print(f"🔄 Duplicates: {self.stats['duplicates']}")
        if self.stats['skipped_non_pdf'] > 0:
            print(f"📄 Non-PDF: {self.stats['skipped_non_pdf']}")
        if self.stats['not_found_404'] > 0:
            print(f"🚫 Not Found: {self.stats['not_found_404']}")
        if self.stats['errors'] > 0:
            print(f"❌ Errors: {self.stats['errors']}")
        print("========================================")
    
    def process_record(self, table_config: Dict, record: Dict) -> bool:
        """Process a single record"""
        try:
            record_id = record['id']
            url = record.get(table_config['url_column'])
            
            if not url:
                return False
            
            # Download PDF
            result = self.download_pdf(url)
            if not result or result[0] is None:
                error_type = result[1] if result else 'unknown_error'

                # Mark record with appropriate status based on error type
                if error_type == 'not_found':
                    self.mark_record_not_found(table_config['name'], record_id)
                elif error_type == 'non_pdf':
                    self.update_record(table_config['name'], record_id, status='non_pdf_file')
                elif error_type == 'invalid_url':
                    self.update_record(table_config['name'], record_id, status='invalid_url')
                else:
                    self.update_record(table_config['name'], record_id, status='download_failed')

                return False

            content, _ = result
            
            self.stats['downloaded'] += 1
            
            # Calculate hash
            pdf_hash = self.calculate_hash(content)
            
            # Check for duplicates
            is_duplicate = self.check_duplicate(table_config['name'], pdf_hash, record_id)
            if is_duplicate:
                self.stats['duplicates'] += 1
                logger.info(f"Duplicate found for record {record_id}")
            
            # Update database
            update_success = self.update_record(table_config['name'], record_id, pdf_hash, is_duplicate)

            # Verify the update worked
            if update_success:
                verification = self.verify_record_update(table_config['name'], record_id)
                if verification and verification.get('pdf_hash') == pdf_hash:
                    logger.info(f"✅ Verified record {record_id} updated successfully")
                    self.stats['processed'] += 1
                    return True
                else:
                    logger.warning(f"⚠️ Update may have failed verification for {record_id}")
                    # Still count as processed if we got some response
                    self.stats['processed'] += 1
                    return True
            else:
                self.stats['errors'] += 1
                return False
                
        except Exception as e:
            logger.error(f"Error processing record: {e}")
            self.stats['errors'] += 1
            return False
    
    def process_table(self, table_key: str, batch_size: int = 50) -> None:
        """Process all records in a table"""
        table_config = self.tables[table_key]
        logger.info(f"Processing {table_key.upper()} table: {table_config['name']}")
        
        while True:
            records = self.get_records(table_config, batch_size)
            if not records:
                break
            
            for record in records:
                self.process_record(table_config, record)
                time.sleep(0.1)  # Rate limiting
            
            time.sleep(1)  # Batch delay
        
        logger.info(f"Completed {table_key.upper()} table processing")
    
    def print_stats(self):
        """Print processing statistics"""
        print("\n=== Processing Statistics ===")
        print(f"✅ Records processed successfully: {self.stats['processed']}")
        print(f"📥 PDFs downloaded and hashed: {self.stats['downloaded']}")
        print(f"🔄 Duplicates found: {self.stats['duplicates']}")
        print()
        print("📊 Skipped/Failed:")
        print(f"   📄 Non-PDF files: {self.stats['skipped_non_pdf']}")
        print(f"   🔗 Invalid URLs: {self.stats['skipped_invalid_url']}")
        print(f"   🚫 Files not found (404): {self.stats['not_found_404']}")
        print(f"   🌐 Network errors: {self.stats['network_errors']}")
        print(f"   ⚠️  Other HTTP errors: {self.stats['other_http_errors']}")
        print(f"   ❌ Other errors: {self.stats['errors']}")
        print()
        total_attempted = sum(self.stats.values())
        success_rate = (self.stats['processed'] / total_attempted * 100) if total_attempted > 0 else 0
        print(f"📈 Success rate: {success_rate:.1f}% ({self.stats['processed']}/{total_attempted})")
        print("=============================")


def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='PDF Hash Processor for Corporate Announcements')
    parser.add_argument('--table', choices=['bse', 'nse', 'all'], default='all',
                       help='Table to process (default: all)')
    parser.add_argument('--limit', type=int, default=50,
                       help='Batch size limit (default: 50)')
    parser.add_argument('--single', nargs=2, metavar=('EXCHANGE', 'RECORD_ID'),
                       help='Process single record: --single bse 12345 or --single nse abcd-1234')

    args = parser.parse_args()
    
    processor = PDFHashProcessor()

    try:
        # Check if single record processing is requested
        if args.single:
            exchange, record_id = args.single
            logger.info(f"Processing single record: {exchange.upper()} ID {record_id}")

            success = processor.process_single_record_by_id(exchange, record_id)

            if success:
                logger.info("✅ Single record processing completed successfully")
            else:
                logger.error("❌ Single record processing failed")

        else:
            # Batch processing
            if args.table == 'all':
                for table_key in ['bse', 'nse']:
                    processor.process_table(table_key, args.limit)
            else:
                processor.process_table(args.table, args.limit)

            processor.print_stats()
            logger.info("Batch processing completed successfully")

    except KeyboardInterrupt:
        logger.info("Processing interrupted by user")
    except Exception as e:
        logger.error(f"Fatal error: {e}")


if __name__ == "__main__":
    main()
