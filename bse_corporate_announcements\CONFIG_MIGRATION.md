# BSE Corporate Announcements Configuration Migration

## Overview

This document describes the migration from the shared configuration system to a dedicated, standalone configuration for the BSE Corporate Announcements feature.

## What Was Changed

### 1. New Configuration File Created

**File:** `bse_config.py`

- **Purpose:** Standalone configuration that doesn't depend on shared modules
- **Benefits:** 
  - Independent of other scrapers
  - Self-contained with all BSE-specific settings
  - No external dependencies on shared modules
  - Easier to maintain and modify

### 2. Configuration Structure

The new `BSECorporateAnnouncementsConfig` class includes:

#### Core Settings
- `BASE_URL`: BSE website URL
- `API_BASE_URL`: BSE API base URL
- `EXCHANGE_NAME`: Display name for the exchange

#### Database Settings
- `SUPABASE_URL`: Supabase project URL
- `SUPABASE_ANON_KEY`: Supabase anonymous key
- `DATABASE_TABLE`: Target database table name

#### API Configuration
- `CORPORATE_ANNOUNCEMENTS_API_URL`: Specific API endpoint
- Default parameters for API requests (category, subcategory, etc.)

#### File Management
- `OUTPUT_FOLDER`: Directory for CSV files
- File naming conventions and extensions
- Date format settings

#### Request Settings
- Timeout configurations
- Retry settings
- Proxy configuration (ScraperAPI)

### 3. Updated Import Statements

All modules in the BSE Corporate Announcements package have been updated to use the new configuration:

#### Files Updated:
- `database_manager.py`
- `file_manager.py`
- `fetcher.py`
- `parser.py`
- `scraper.py`
- `session.py`
- `cli_commands.py`
- `cli_parser.py`
- `__init__.py`

#### Import Change:
```python
# Old import
from .config import config

# New import  
from .bse_config import bse_config as config
```

### 4. Backward Compatibility

The new configuration maintains backward compatibility by exporting commonly used settings as module-level variables:

```python
# Export commonly used settings for backward compatibility
BASE_URL = bse_config.BASE_URL
API_BASE_URL = bse_config.API_BASE_URL
SUPABASE_URL = bse_config.SUPABASE_URL
DATABASE_TABLE = bse_config.DATABASE_TABLE
# ... and more
```

## Benefits of the New Configuration

### 1. Independence
- No dependency on shared modules
- Can be modified without affecting other scrapers
- Self-contained configuration

### 2. Clarity
- All BSE-specific settings in one place
- Clear separation from other exchange configurations
- Easier to understand and maintain

### 3. Flexibility
- Easy to add BSE-specific features
- No conflicts with shared configuration changes
- Independent versioning and updates

### 4. Maintainability
- Simpler debugging
- Isolated configuration changes
- Reduced coupling between modules

## Configuration Methods

The new configuration class provides helpful methods:

### Database Operations
```python
config.get_database_headers()    # Get Supabase API headers
config.get_database_url()        # Get database REST API URL
```

### File Operations
```python
config.get_output_path(filename) # Get full file path
config.get_filename_timestamp()  # Get timestamp for filenames
```

### API Operations
```python
config.get_api_url()             # Get complete API URL
config.format_date_for_api(date) # Format date for BSE API
```

### Proxy Operations
```python
config.get_proxy_config()        # Get proxy configuration
config.get_scraperapi_url(url)   # Get ScraperAPI URL
```

## Testing

A test script `test_bse_config.py` has been created to verify:
- Configuration loading
- Method functionality
- Database manager integration
- Module import compatibility

## Migration Verification

The migration has been tested and verified:
- ✅ Configuration loads successfully
- ✅ Database manager initializes correctly
- ✅ All modules import the new configuration
- ✅ Backward compatibility maintained

## Usage Examples

### Basic Usage
```python
from bse_corporate_announcements.bse_config import bse_config

# Access configuration
print(bse_config.EXCHANGE_NAME)
print(bse_config.DATABASE_TABLE)

# Use configuration methods
headers = bse_config.get_database_headers()
api_url = bse_config.get_api_url()
```

### Advanced Usage
```python
from bse_corporate_announcements import config

# Access through module import (backward compatible)
print(config.bse_config.BASE_URL)

# Use exported constants
from bse_corporate_announcements.bse_config import SUPABASE_URL, DATABASE_TABLE
```

## Future Considerations

1. **Environment Variables**: Consider adding support for environment variable overrides
2. **Configuration Validation**: Enhanced validation for critical settings
3. **Logging Configuration**: Centralized logging configuration
4. **Feature Flags**: Support for enabling/disabling features

## Conclusion

The migration to a dedicated BSE configuration provides better separation of concerns, improved maintainability, and greater flexibility for future enhancements while maintaining full backward compatibility.
