#!/usr/bin/env python3
"""
Example usage of the enhanced AI Document Analyzer with flexible document type system.

This script demonstrates:
1. Using the enhanced document type classification
2. Adding custom document types at runtime
3. Loading document types from configuration files
4. Getting comprehensive statistics
5. Backward compatibility with legacy code
"""

import json
from ai_document_analyzer import DocumentAnalyzer, DocumentTypeDefinition, DEFAULT_CONFIG

def demonstrate_enhanced_features():
    """Demonstrate the enhanced features of the document analyzer."""
    
    print("=== Enhanced AI Document Analyzer Demo ===\n")
    
    # Initialize analyzer with default configuration
    config = DEFAULT_CONFIG.copy()
    analyzer = DocumentAnalyzer(config)
    
    print("1. Document Type Registry Information:")
    print(f"   Registered types: {len(analyzer.document_registry.document_types)}")
    print(f"   Available types: {analyzer.document_registry.list_document_types()}")
    print()
    
    # Demonstrate classification with different document types
    test_documents = [
        {
            "headline": "Q3 FY2024 Earnings Call Scheduled",
            "details": "Company announces investor conference call for quarterly results",
            "content": "The company will host an earnings call on March 15, 2024 to discuss Q3 FY2024 financial results with investors and analysts.",
            "expected_type": "earnings_call"
        },
        {
            "headline": "Dividend Declaration",
            "details": "Board approves interim dividend payment",
            "content": "The Board of Directors has declared an interim dividend of Rs. 5 per share for the financial year 2024. Record date is March 20, 2024.",
            "expected_type": "dividend_announcement"
        },
        {
            "headline": "Strategic Partnership Announcement",
            "details": "Company enters into new business collaboration",
            "content": "We are pleased to announce a strategic partnership with TechCorp to expand our digital capabilities and market reach.",
            "expected_type": "press_release"
        },
        {
            "headline": "Unusual Document Type",
            "details": "Something that doesn't fit standard categories",
            "content": "This is a document with content that doesn't clearly match any predefined document type categories.",
            "expected_type": "unknown"
        }
    ]
    
    print("2. Document Classification Examples:")
    for i, doc in enumerate(test_documents, 1):
        print(f"\n   Example {i}: {doc['headline']}")
        doc_type_id, classification_result = analyzer.classify_document_type(
            doc['content'], doc['headline'], doc['details'], f"demo_{i}"
        )
        
        print(f"   Classified as: {doc_type_id}")
        if classification_result:
            print(f"   Confidence: {classification_result.confidence_score:.3f}")
            print(f"   Method: {classification_result.classification_method}")
            print(f"   Keywords matched: {classification_result.matched_keywords}")
            print(f"   Fallback used: {classification_result.fallback_used}")
        print(f"   Expected: {doc['expected_type']}")
        print(f"   Match: {'✓' if doc_type_id == doc['expected_type'] else '✗'}")
    
    print("\n3. Adding Custom Document Type:")
    custom_type = {
        "type_id": "sustainability_report",
        "display_name": "Sustainability Report",
        "keywords": ["sustainability", "esg", "environmental", "social", "governance"],
        "weighted_keywords": {
            "sustainability": 2.0,
            "esg": 2.0,
            "environmental": 1.5,
            "carbon": 1.3,
            "renewable": 1.2
        },
        "template": {
            "type": "sustainability_report",
            "report_period": "annual/quarterly",
            "esg_highlights": "key sustainability achievements",
            "carbon_footprint": "emissions data if available",
            "summary": "concise description",
            "confidence_score": 0.85
        },
        "validation_rules": {
            "required_fields": ["report_period"],
            "field_types": {
                "report_period": "string",
                "esg_highlights": "string"
            }
        },
        "confidence_threshold": 0.75,
        "priority": 6,
        "enabled": True
    }
    
    success = analyzer.add_custom_document_type(custom_type)
    print(f"   Custom type added: {'✓' if success else '✗'}")
    print(f"   Total types now: {len(analyzer.document_registry.document_types)}")
    
    # Test the custom type
    sustainability_doc = {
        "headline": "Annual Sustainability Report 2024",
        "details": "Company publishes comprehensive ESG report",
        "content": "Our annual sustainability report highlights our environmental initiatives, carbon reduction efforts, and social responsibility programs for 2024."
    }
    
    custom_type_id, custom_result = analyzer.classify_document_type(
        sustainability_doc['content'], 
        sustainability_doc['headline'], 
        sustainability_doc['details'],
        "custom_demo"
    )
    
    print(f"\n   Testing custom type:")
    print(f"   Classified as: {custom_type_id}")
    if custom_result:
        print(f"   Confidence: {custom_result.confidence_score:.3f}")
        print(f"   Keywords matched: {custom_result.matched_keywords}")
    
    print("\n4. Comprehensive Statistics:")
    stats = analyzer.get_comprehensive_stats()
    print(f"   Total processed: {stats.get('total_processed', 0)}")
    print(f"   Classification methods: {stats.get('classification_methods', {})}")
    print(f"   Document types found: {stats.get('by_type', {})}")
    
    classification_details = stats.get('classification_details', {})
    if classification_details and 'total_classifications' in classification_details:
        print(f"   Classification success rate: {classification_details.get('success_rate', 'N/A')}")
        print(f"   Fallback usage rate: {classification_details.get('fallback_rate', 'N/A')}")
    
    print("\n5. Backward Compatibility:")
    # Show that legacy methods still work
    legacy_examples = analyzer.get_document_type_examples()
    print(f"   Legacy format examples available: {len(legacy_examples)}")
    
    # Test legacy classification method
    legacy_result = analyzer.legacy_classify_document_type(
        "This is an earnings call announcement for Q4 results",
        "Q4 Earnings Call",
        "Quarterly investor call"
    )
    print(f"   Legacy classification result: {legacy_result}")
    print(f"   Legacy result type: {type(legacy_result)}")
    
    print("\n6. Configuration File Support:")
    print("   The system can load document types from:")
    print("   - document_types.yaml (YAML format)")
    print("   - document_types.json (JSON format)")
    print("   - config/document_types.yaml")
    print("   - config/document_types.json")
    print("   - Custom paths via reload_document_types_config()")
    
    # Try to reload from the created config file
    reload_success = analyzer.reload_document_types_config('document_types.yaml')
    print(f"   Config reload from document_types.yaml: {'✓' if reload_success else '✗'}")
    
    print("\n=== Demo Complete ===")
    print("\nKey Improvements:")
    print("✓ Flexible document type system with runtime extensibility")
    print("✓ Enhanced classification with fuzzy matching and weighted keywords")
    print("✓ Adaptive confidence scoring with multiple quality factors")
    print("✓ Comprehensive error handling and detailed logging")
    print("✓ Configuration-based document type definitions")
    print("✓ Backward compatibility with existing code")
    print("✓ Robust fallback mechanisms for unknown document types")

if __name__ == "__main__":
    try:
        demonstrate_enhanced_features()
    except Exception as e:
        print(f"Demo failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
