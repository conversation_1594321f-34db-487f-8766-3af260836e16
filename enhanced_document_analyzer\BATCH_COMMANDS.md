# Batch Processing Commands - Enhanced Document Analyzer

## 🚀 Quick Start Commands

### 1. Preview Database Status
```bash
python generate_all_summaries.py --preview
```
Shows total records, processed records, and processing estimates.

### 2. Process All Unprocessed Records (Recommended)
```bash
python generate_all_summaries.py --process-unprocessed-only
```
Processes only records that don't have summaries yet.

### 3. Process ALL Records (Including Already Processed)
```bash
python generate_all_summaries.py --process-all
```
⚠️ **Use with caution** - This will reprocess everything!

### 4. Resume Interrupted Processing
```bash
python generate_all_summaries.py --resume
```
Continues from where previous processing was interrupted.

### 5. Check Processing Status
```bash
python generate_all_summaries.py --status
```
Shows current progress of ongoing/completed processing.

## 🔧 Advanced Commands

### Custom Batch Size
```bash
# Process with smaller batches (safer, slower)
python generate_all_summaries.py --process-unprocessed-only --batch-size 25

# Process with larger batches (faster, more resource intensive)
python generate_all_summaries.py --process-unprocessed-only --batch-size 100
```

### Dry Run (Preview Only)
```bash
python generate_all_summaries.py --process-unprocessed-only --dry-run
```
Shows what would be processed without actually doing it.

### Debug Mode
```bash
python generate_all_summaries.py --process-unprocessed-only --log-level DEBUG
```
Enables detailed logging for troubleshooting.

## 📊 Using the Original Script

### Process Specific Records
```bash
# Single record
python ai_document_analyzer.py --ids 180ecf34-12c8-4d41-a6a6-89705a0551a0

# Multiple records
python ai_document_analyzer.py --ids "id1,id2,id3"
```

### Batch Processing with Original Script
```bash
# Process 100 unprocessed records
python ai_document_analyzer.py --batch-process --limit 100

# Process ALL records (new feature)
python ai_document_analyzer.py --process-all --limit 50

# Skip existing summaries
python ai_document_analyzer.py --batch-process --skip-existing --limit 100

# Force reprocess existing summaries
python ai_document_analyzer.py --batch-process --force --limit 50

# Resume from specific record
python ai_document_analyzer.py --batch-process --resume-from RECORD_ID

# Save progress for resuming
python ai_document_analyzer.py --process-all --save-progress --progress-file my_progress.json
```

## 🎯 Recommended Workflow

### First Time Processing
1. **Check status**: `python generate_all_summaries.py --preview`
2. **Start processing**: `python generate_all_summaries.py --process-unprocessed-only --batch-size 50`
3. **Monitor progress**: Check logs or use `--status` command

### If Processing Gets Interrupted
1. **Check status**: `python generate_all_summaries.py --status`
2. **Resume**: `python generate_all_summaries.py --resume`

### Regular Maintenance
1. **Check for new records**: `python generate_all_summaries.py --preview`
2. **Process new records**: `python generate_all_summaries.py --process-unprocessed-only`

## ⚙️ Configuration Options

### Batch Size Guidelines
- **Small (10-25)**: Safer, less memory usage, slower
- **Medium (50-75)**: Balanced performance and safety
- **Large (100+)**: Faster but requires more resources

### Progress Tracking
- Progress is automatically saved to `processing_progress.json`
- Can be resumed if processing is interrupted
- Includes detailed statistics and error tracking

### Error Handling
- Failed records are tracked and logged
- Processing continues even if some records fail
- Detailed error logs saved to timestamped files

## 🔍 Monitoring and Troubleshooting

### Check Logs
```bash
# View latest log file
ls -la ai_analysis_*.log | tail -1

# Monitor processing in real-time (Linux/Mac)
tail -f ai_analysis_*.log

# Search for errors
grep -i error ai_analysis_*.log
```

### Progress Files
- `processing_progress.json` - Current progress and statistics
- `ai_analysis_YYYYMMDD_HHMMSS.log` - Detailed processing logs

### Common Issues
1. **Memory issues**: Reduce batch size
2. **Network timeouts**: Check internet connection, reduce batch size
3. **API rate limits**: Built-in delays handle this automatically
4. **Database connection**: Check Supabase credentials

## 📈 Performance Tips

1. **Optimal batch size**: Start with 50, adjust based on performance
2. **Run during off-peak hours**: Less network congestion
3. **Monitor system resources**: Ensure adequate memory and CPU
4. **Use resume feature**: Don't restart from beginning if interrupted
5. **Regular status checks**: Monitor progress and success rates

## 🚨 Important Notes

- **Backup recommended**: Consider backing up your database before large batch operations
- **API limits**: The system respects rate limits but large batches may take time
- **Resource usage**: Monitor system resources during large batch processing
- **Progress saving**: Always enabled for batch operations to allow resuming
- **Error resilience**: Processing continues even if individual records fail

## 📞 Support

If you encounter issues:
1. Check the log files for detailed error messages
2. Try reducing batch size
3. Use dry-run mode to test configuration
4. Check database connectivity and API credentials
