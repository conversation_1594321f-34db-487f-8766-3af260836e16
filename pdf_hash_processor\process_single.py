#!/usr/bin/env python3
"""
Simple utility for processing single records
Makes it easy to process individual records by ID
"""

import sys
import argparse
from pdf_hash_standalone import PDFHashProcessor
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def main():
    """Simple interface for single record processing"""
    parser = argparse.ArgumentParser(
        description='Process a single PDF hash record',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python process_single.py bse 12345
  python process_single.py nse abcd-1234-efgh-5678
  
This will:
  • Fetch the record from the database
  • Download the PDF from the attachment URL
  • Calculate SHA256 hash of the PDF content
  • Check for duplicates in the same table
  • Update the database with results
        """
    )
    
    parser.add_argument('exchange', choices=['bse', 'nse'], 
                       help='Exchange type (bse or nse)')
    parser.add_argument('record_id', 
                       help='Record ID to process')
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='Enable verbose logging')
    
    args = parser.parse_args()
    
    # Set logging level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    print(f"Processing {args.exchange.upper()} record: {args.record_id}")
    print("=" * 50)
    
    try:
        processor = PDFHashProcessor()
        success = processor.process_single_record_by_id(args.exchange, args.record_id)
        
        if success:
            print("\n✅ Processing completed successfully!")
        else:
            print("\n❌ Processing failed!")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n⚠️ Processing interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
