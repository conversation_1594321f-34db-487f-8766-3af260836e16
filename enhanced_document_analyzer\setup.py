#!/usr/bin/env python3
"""
Setup script for Enhanced AI Document Analyzer
"""

import subprocess
import sys
import os
from pathlib import Path

def run_command(command, description):
    """Run a command and handle errors."""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed: {e}")
        if e.stdout:
            print(f"STDOUT: {e.stdout}")
        if e.stderr:
            print(f"STDERR: {e.stderr}")
        return False

def check_python_version():
    """Check if Python version is compatible."""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print(f"❌ Python 3.8+ required, found {version.major}.{version.minor}")
        return False
    print(f"✅ Python {version.major}.{version.minor}.{version.micro} is compatible")
    return True

def install_requirements():
    """Install required packages."""
    requirements_file = Path("requirements.txt")
    if not requirements_file.exists():
        print("❌ requirements.txt not found")
        return False
    
    return run_command(
        f"{sys.executable} -m pip install -r requirements.txt",
        "Installing requirements"
    )

def verify_installation():
    """Verify that key packages are installed."""
    required_packages = {
        "requests": "requests",
        "supabase": "supabase",
        "PyPDF2": "PyPDF2",
        "pdfplumber": "pdfplumber",
        "PyYAML": "yaml"
    }

    print("🔍 Verifying installation...")
    for display_name, import_name in required_packages.items():
        try:
            __import__(import_name)
            print(f"✅ {display_name} installed")
        except ImportError:
            print(f"❌ {display_name} not found")
            return False

    return True

def check_config_files():
    """Check if configuration files exist."""
    config_files = ["document_types.yaml", "ai_document_analyzer.py"]
    
    print("📁 Checking configuration files...")
    for file in config_files:
        if Path(file).exists():
            print(f"✅ {file} found")
        else:
            print(f"❌ {file} missing")
            return False
    
    return True

def run_test():
    """Run a basic test to ensure everything works."""
    print("🧪 Running basic functionality test...")
    
    try:
        # Import the main module
        sys.path.insert(0, ".")
        from ai_document_analyzer import DocumentAnalyzer, DEFAULT_CONFIG
        
        # Initialize analyzer
        analyzer = DocumentAnalyzer(DEFAULT_CONFIG)
        
        # Test classification
        doc_type_id, result = analyzer.classify_document_type(
            "This is a test earnings call announcement",
            "Test Earnings Call",
            "Test details"
        )
        
        if doc_type_id and result:
            print(f"✅ Test classification successful: {doc_type_id} (confidence: {result.confidence_score:.3f})")
            return True
        else:
            print("❌ Test classification failed")
            return False
            
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        return False

def main():
    """Main setup function."""
    print("🚀 Enhanced AI Document Analyzer Setup")
    print("=" * 50)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Install requirements
    if not install_requirements():
        print("❌ Failed to install requirements. Please check your internet connection and try again.")
        sys.exit(1)
    
    # Verify installation
    if not verify_installation():
        print("❌ Installation verification failed. Please check the error messages above.")
        sys.exit(1)
    
    # Check config files
    if not check_config_files():
        print("❌ Configuration files missing. Please ensure all files are in the correct location.")
        sys.exit(1)
    
    # Run test
    if not run_test():
        print("❌ Basic functionality test failed. Please check the error messages above.")
        sys.exit(1)
    
    print("\n🎉 Setup completed successfully!")
    print("\n📖 Next steps:")
    print("1. Set environment variables (optional):")
    print("   - SUPABASE_KEY=your_supabase_key")
    print("   - OPENROUTER_API_KEY=your_openrouter_key")
    print("\n2. Test with a record:")
    print("   python ai_document_analyzer.py --ids YOUR_RECORD_ID")
    print("\n3. Run the demo:")
    print("   python example_usage.py")
    print("\n4. See README.md for more usage examples")

if __name__ == "__main__":
    main()
