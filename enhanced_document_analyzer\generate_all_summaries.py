#!/usr/bin/env python3
"""
Generate Summaries for All Records - Enhanced Document Analyzer

This script provides easy-to-use commands for generating summaries for all records
in the corporate announcements database with comprehensive progress tracking.

Usage Examples:
    python generate_all_summaries.py --help
    python generate_all_summaries.py --preview
    python generate_all_summaries.py --process-all
    python generate_all_summaries.py --process-unprocessed-only
    python generate_all_summaries.py --resume
    python generate_all_summaries.py --status
"""

import argparse
import json
import os
import sys
from datetime import datetime
from ai_document_analyzer import DocumentAnalyzer, DEFAULT_CONFIG

def print_banner():
    """Print application banner."""
    print("=" * 70)
    print("🤖 ENHANCED DOCUMENT ANALYZER - BATCH SUMMARY GENERATOR")
    print("=" * 70)
    print()

def get_database_status(analyzer):
    """Get current database status."""
    total_records = analyzer.get_all_record_count()
    unprocessed_records = analyzer.get_unprocessed_count()
    processed_records = total_records - unprocessed_records
    
    return {
        'total': total_records,
        'processed': processed_records,
        'unprocessed': unprocessed_records,
        'completion_rate': (processed_records / total_records * 100) if total_records > 0 else 0
    }

def show_preview(analyzer):
    """Show preview of what would be processed."""
    print("📊 DATABASE STATUS PREVIEW")
    print("-" * 40)
    
    status = get_database_status(analyzer)
    
    print(f"Total records in database: {status['total']:,}")
    print(f"Records with summaries: {status['processed']:,}")
    print(f"Records with NULL summary: {status['unprocessed']:,}")
    print(f"Completion rate: {status['completion_rate']:.1f}%")
    print()
    print("ℹ️  Processing will ONLY target records where summary IS NULL")
    print()
    
    if status['unprocessed'] > 0:
        print("🎯 PROCESSING ESTIMATES")
        print("-" * 40)
        batch_sizes = [10, 25, 50, 100]
        for batch_size in batch_sizes:
            batches = (status['unprocessed'] + batch_size - 1) // batch_size
            est_time_min = batches * 2  # Rough estimate: 2 minutes per batch
            est_time_hours = est_time_min / 60
            print(f"Batch size {batch_size:3d}: {batches:4d} batches, ~{est_time_hours:.1f} hours")
    else:
        print("✅ All records have been processed!")

def show_progress_status():
    """Show current progress status."""
    progress_file = 'processing_progress.json'
    
    if not os.path.exists(progress_file):
        print("❌ No progress file found. No processing has been started yet.")
        return
    
    try:
        with open(progress_file, 'r') as f:
            progress = json.load(f)
        
        print("📈 CURRENT PROGRESS STATUS")
        print("-" * 40)
        print(f"Last updated: {progress.get('timestamp', 'Unknown')}")
        print(f"Processed records: {len(progress.get('processed_ids', []))}")
        print(f"Failed records: {len(progress.get('failed_ids', []))}")
        print(f"Current batch: {progress.get('current_batch', 0)}")
        print(f"Total batches: {progress.get('total_batches', 0)}")
        
        if progress.get('total_batches', 0) > 0:
            completion = (progress.get('current_batch', 0) / progress.get('total_batches', 1)) * 100
            print(f"Completion: {completion:.1f}%")
        
        # Show recent stats
        stats = progress.get('stats', {})
        if stats:
            print(f"\nDocument type distribution:")
            for doc_type, count in stats.get('by_type', {}).items():
                print(f"  {doc_type}: {count}")
        
    except Exception as e:
        print(f"❌ Error reading progress file: {str(e)}")

def main():
    """Main function."""
    parser = argparse.ArgumentParser(
        description="Generate summaries for all corporate announcement records",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python generate_all_summaries.py --preview
      Show database status and processing estimates
  
  python generate_all_summaries.py --process-unprocessed-only
      Process only records that don't have summaries yet
  
  python generate_all_summaries.py --process-all --batch-size 25
      Process ALL records with batch size of 25
  
  python generate_all_summaries.py --resume
      Resume interrupted processing from where it left off
  
  python generate_all_summaries.py --status
      Show current processing progress
        """
    )
    
    parser.add_argument('--preview', action='store_true',
                       help='Show database status and processing estimates')
    
    parser.add_argument('--process-all', action='store_true',
                       help='Process ALL records (including already processed ones)')
    
    parser.add_argument('--process-unprocessed-only', action='store_true',
                       help='Process only records where summary IS NULL (recommended)')

    parser.add_argument('--resume', action='store_true',
                       help='Resume interrupted processing from progress file')
    
    parser.add_argument('--status', action='store_true',
                       help='Show current processing progress status')
    
    parser.add_argument('--batch-size', type=int, default=50,
                       help='Number of records to process in each batch (default: 50)')
    
    parser.add_argument('--dry-run', action='store_true',
                       help='Show what would be processed without actually doing it')
    
    parser.add_argument('--log-level', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       default='INFO', help='Set logging level')
    
    args = parser.parse_args()
    
    print_banner()
    
    # Handle status check
    if args.status:
        show_progress_status()
        return
    
    # Initialize analyzer
    try:
        config = DEFAULT_CONFIG.copy()
        config['log_level'] = args.log_level
        analyzer = DocumentAnalyzer(config)
    except Exception as e:
        print(f"❌ Error initializing analyzer: {str(e)}")
        sys.exit(1)
    
    # Handle preview
    if args.preview:
        show_preview(analyzer)
        return
    
    # Validate arguments
    if not any([args.process_all, args.process_unprocessed_only, args.resume]):
        print("❌ Please specify one of: --process-all, --process-unprocessed-only, or --resume")
        print("   Use --help for more information")
        sys.exit(1)
    
    # Handle resume
    if args.resume:
        if not os.path.exists('processing_progress.json'):
            print("❌ No progress file found. Cannot resume.")
            print("   Use --process-unprocessed-only to start fresh processing")
            sys.exit(1)
        
        print("🔄 Resuming processing from previous session...")
        skip_existing = True  # When resuming, typically skip existing
    
    # Handle process all vs unprocessed only
    elif args.process_all:
        print("⚠️  Processing ALL records (including already processed ones)")
        skip_existing = False
    else:  # process_unprocessed_only
        print("🎯 Processing only unprocessed records")
        skip_existing = True
    
    # Show what will be processed
    status = get_database_status(analyzer)
    records_to_process = status['unprocessed'] if skip_existing else status['total']
    
    print(f"\n📋 PROCESSING PLAN")
    print("-" * 40)
    print(f"Records to process: {records_to_process:,}")
    print(f"Batch size: {args.batch_size}")
    print(f"Estimated batches: {(records_to_process + args.batch_size - 1) // args.batch_size}")
    print(f"Skip existing summaries: {skip_existing}")
    print(f"Dry run mode: {args.dry_run}")
    
    if args.dry_run:
        print("\n✅ DRY RUN COMPLETE - No actual processing performed")
        return
    
    # Confirm before processing
    if records_to_process > 100:
        print(f"\n⚠️  You are about to process {records_to_process:,} records.")
        confirm = input("Do you want to continue? (yes/no): ").lower().strip()
        if confirm != 'yes':
            print("Operation cancelled")
            return
    
    # Start processing
    print(f"\n🚀 Starting processing...")
    print(f"⏰ Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        final_stats = analyzer.process_all_records(
            batch_size=args.batch_size,
            skip_existing=skip_existing,
            save_progress=True,
            progress_file='processing_progress.json'
        )
        
        print(f"\n🎉 PROCESSING COMPLETED SUCCESSFULLY!")
        print(f"📊 Final Results:")
        print(f"   Total records: {final_stats['total_records']:,}")
        print(f"   Successfully processed: {final_stats['processed']:,}")
        print(f"   Failed: {final_stats['failed']:,}")
        print(f"   Success rate: {final_stats['success_rate']:.1f}%")
        print(f"   Batches completed: {final_stats['batches_completed']}/{final_stats['total_batches']}")
        
        # Show comprehensive stats
        comprehensive_stats = analyzer.get_comprehensive_stats()
        print(f"\n📈 Document Type Distribution:")
        for doc_type, count in comprehensive_stats.get('by_type', {}).items():
            print(f"   {doc_type}: {count}")
        
        print(f"\n⏰ Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
    except KeyboardInterrupt:
        print(f"\n⚠️  Processing interrupted by user")
        print("   Progress has been saved. Use --resume to continue later.")
    except Exception as e:
        print(f"\n❌ Error during processing: {str(e)}")
        print("   Check the log files for detailed error information.")
        sys.exit(1)

if __name__ == "__main__":
    main()
