#!/usr/bin/env python3
"""
Test script for single record processing feature
"""

import requests
import json
import logging
from pdf_hash_standalone import PDFHashProcessor

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Supabase configuration
SUPABASE_URL = "https://yvuwseolouiqhoxsieop.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl2dXdzZW9sb3VpcWhveHNpZW9wIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI1OTI2ODcsImV4cCI6MjA2ODE2ODY4N30.W7m701xSGJ5eh8oc4turmb4zO9-nz1Pbzqz-DL9EEow"


def get_sample_records():
    """Get sample record IDs from both tables"""
    headers = {
        'apikey': SUPABASE_KEY,
        'Authorization': f'Bearer {SUPABASE_KEY}',
        'Content-Type': 'application/json'
    }
    
    sample_records = {'bse': [], 'nse': []}
    
    # Get BSE sample records
    try:
        url = f"{SUPABASE_URL}/rest/v1/bse_corporate_announcements"
        params = {
            'select': 'id,attachmentfile,pdf_hash',
            'attachmentfile': 'not.is.null',
            'limit': '3'
        }
        
        response = requests.get(url, headers=headers, params=params, timeout=10)
        response.raise_for_status()
        bse_records = response.json()
        
        for record in bse_records:
            sample_records['bse'].append({
                'id': record['id'],
                'has_attachment': bool(record.get('attachmentfile')),
                'has_hash': bool(record.get('pdf_hash'))
            })
        
        logger.info(f"Found {len(bse_records)} BSE sample records")
        
    except Exception as e:
        logger.error(f"Error fetching BSE records: {e}")
    
    # Get NSE sample records
    try:
        url = f"{SUPABASE_URL}/rest/v1/nse_corporate_announcements"
        params = {
            'select': 'id,attachment_url,pdf_hash',
            'attachment_url': 'not.is.null',
            'limit': '3'
        }
        
        response = requests.get(url, headers=headers, params=params, timeout=10)
        response.raise_for_status()
        nse_records = response.json()
        
        for record in nse_records:
            sample_records['nse'].append({
                'id': record['id'],
                'has_attachment': bool(record.get('attachment_url')),
                'has_hash': bool(record.get('pdf_hash'))
            })
        
        logger.info(f"Found {len(nse_records)} NSE sample records")
        
    except Exception as e:
        logger.error(f"Error fetching NSE records: {e}")
    
    return sample_records


def test_single_record_processing():
    """Test the single record processing feature"""
    print("Testing Single Record Processing Feature")
    print("=" * 50)
    
    # Get sample records
    sample_records = get_sample_records()
    
    if not sample_records['bse'] and not sample_records['nse']:
        print("❌ No sample records found to test with")
        return
    
    processor = PDFHashProcessor()
    
    # Test with BSE record
    if sample_records['bse']:
        bse_record = sample_records['bse'][0]
        print(f"\n🔍 Testing BSE Record: {bse_record['id']}")
        print(f"   Has attachment: {bse_record['has_attachment']}")
        print(f"   Has hash: {bse_record['has_hash']}")
        
        # Test fetching the record
        record_data = processor.get_single_record('bse', bse_record['id'])
        if record_data:
            print(f"   ✅ Successfully fetched record data")
            print(f"   Attachment URL: {record_data.get('attachmentfile', 'None')[:50]}...")
        else:
            print(f"   ❌ Failed to fetch record data")
    
    # Test with NSE record
    if sample_records['nse']:
        nse_record = sample_records['nse'][0]
        print(f"\n🔍 Testing NSE Record: {nse_record['id']}")
        print(f"   Has attachment: {nse_record['has_attachment']}")
        print(f"   Has hash: {nse_record['has_hash']}")
        
        # Test fetching the record
        record_data = processor.get_single_record('nse', nse_record['id'])
        if record_data:
            print(f"   ✅ Successfully fetched record data")
            print(f"   Attachment URL: {record_data.get('attachment_url', 'None')[:50]}...")
        else:
            print(f"   ❌ Failed to fetch record data")
    
    print(f"\n📝 Usage Examples:")
    if sample_records['bse']:
        print(f"   python pdf_hash_standalone.py --single bse {sample_records['bse'][0]['id']}")
    if sample_records['nse']:
        print(f"   python pdf_hash_standalone.py --single nse {sample_records['nse'][0]['id']}")


def test_invalid_inputs():
    """Test error handling for invalid inputs"""
    print("\n" + "=" * 50)
    print("Testing Error Handling")
    print("=" * 50)
    
    processor = PDFHashProcessor()
    
    # Test invalid exchange
    print("\n🔍 Testing invalid exchange...")
    result = processor.get_single_record('invalid', '12345')
    print(f"   Result: {'❌ Correctly rejected' if not result else '⚠️ Unexpected success'}")
    
    # Test non-existent record ID
    print("\n🔍 Testing non-existent record ID...")
    result = processor.get_single_record('bse', 'non-existent-id-12345')
    print(f"   Result: {'❌ Correctly not found' if not result else '⚠️ Unexpected success'}")


def main():
    """Run all tests"""
    print("PDF Hash Processor - Single Record Processing Test")
    print("=" * 60)
    
    try:
        # Test 1: Single record processing
        test_single_record_processing()
        
        # Test 2: Error handling
        test_invalid_inputs()
        
        print("\n" + "=" * 60)
        print("✅ All tests completed!")
        
        print("\n📖 How to use single record processing:")
        print("   python pdf_hash_standalone.py --single bse <record_id>")
        print("   python pdf_hash_standalone.py --single nse <record_id>")
        print("\n   This will:")
        print("   • Fetch the specific record from the database")
        print("   • Download and hash the PDF if not already done")
        print("   • Check for duplicates")
        print("   • Update the database with results")
        
    except Exception as e:
        logger.error(f"Test failed: {e}")


if __name__ == "__main__":
    main()
