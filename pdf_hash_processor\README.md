# PDF Hash Processor for Corporate Announcements

This collection of scripts processes PDF attachments from corporate announcements tables, calculates SHA256 hashes, and updates duplicate detection status.

## Overview

The PDF Hash Processor performs the following operations:

1. **Downloads PDFs** from attachment URLs in corporate announcements tables
2. **Calculates SHA256 hashes** of the PDF content
3. **Updates the `pdf_hash` column** with the calculated hash
4. **Checks for duplicate hashes** within the same table
5. **Updates `is_duplicate` and `duplicate_check_status` columns** accordingly

## Supported Tables

- **BSE Corporate Announcements**: `bse_corporate_announcements`
  - URL Column: `attachmentfile`
- **NSE Corporate Announcements**: `nse_corporate_announcements`
  - URL Column: `attachment_url`

## Files Included

### 1. `pdf_hash_processor.py`
Full-featured processor that integrates with the existing codebase structure.
- Uses shared configuration from `shared.base_config`
- Includes comprehensive logging
- Supports batch processing with configurable sizes

### 2. `pdf_hash_standalone.py` ⭐ **Recommended**
Standalone version that can be run independently without dependencies on the existing codebase.
- Self-contained with all necessary configurations
- Command-line interface with options
- Easier to deploy and run

### 3. `test_pdf_processor.py`
Test script to verify functionality before running the actual processor.
- Tests database connectivity
- Validates hash calculation
- Retrieves sample records
- Tests PDF download functionality

## Quick Start

### Step 1: Test the Setup
```bash
python test_pdf_processor.py
```

### Step 2: Run the Processor

#### Batch Processing
```bash
# Process both BSE and NSE tables
python pdf_hash_standalone.py

# Process only BSE table
python pdf_hash_standalone.py --table bse

# Process only NSE table
python pdf_hash_standalone.py --table nse

# Process with smaller batch size
python pdf_hash_standalone.py --limit 25
```

#### Single Record Processing ⭐ **New Feature**
```bash
# Process specific BSE record
python pdf_hash_standalone.py --single bse 12345

# Process specific NSE record
python pdf_hash_standalone.py --single nse abcd-1234-efgh-5678

# Alternative: Use the simple utility
python process_single.py bse 12345
python process_single.py nse abcd-1234-efgh-5678
```

## Command Line Options

### Batch Processing
```bash
python pdf_hash_standalone.py [OPTIONS]

Options:
  --table {bse,nse,all}  Table to process (default: all)
  --limit INTEGER        Batch size limit (default: 50)
  --help                Show help message
```

### Single Record Processing
```bash
python pdf_hash_standalone.py --single EXCHANGE RECORD_ID

Arguments:
  EXCHANGE              Exchange type: 'bse' or 'nse'
  RECORD_ID            The specific record ID to process

Examples:
  python pdf_hash_standalone.py --single bse 12345
  python pdf_hash_standalone.py --single nse abcd-1234-efgh-5678
```

### Simple Utility
```bash
python process_single.py EXCHANGE RECORD_ID [--verbose]

Examples:
  python process_single.py bse 12345
  python process_single.py nse abcd-1234-efgh-5678 --verbose
```

## Configuration

### Supabase Settings
Update the following constants in the scripts if needed:

```python
SUPABASE_URL = "https://ratzkumhtswilixzfcgl.supabase.co"
SUPABASE_KEY = "your-supabase-anon-key"
```

### Processing Parameters
- **Batch Size**: Number of records processed in each batch (default: 50)
- **Request Timeout**: 30 seconds for PDF downloads
- **Rate Limiting**: 0.1 second delay between records, 1 second between batches

## Database Schema Requirements

The scripts expect the following columns in both tables:

### Required Columns
- `id`: Primary key (UUID or similar)
- `attachmentfile` (BSE) / `attachment_url` (NSE): URL to PDF attachment
- `pdf_hash`: VARCHAR column to store calculated hash
- `is_duplicate`: BOOLEAN column for duplicate status
- `duplicate_check_status`: VARCHAR column for processing status
- `updated_at`: TIMESTAMP column for last update time

### Example Schema Update
```sql
-- Add columns if they don't exist
ALTER TABLE bse_corporate_announcements 
ADD COLUMN IF NOT EXISTS pdf_hash VARCHAR,
ADD COLUMN IF NOT EXISTS is_duplicate BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS duplicate_check_status VARCHAR;

ALTER TABLE nse_corporate_announcements 
ADD COLUMN IF NOT EXISTS pdf_hash VARCHAR,
ADD COLUMN IF NOT EXISTS is_duplicate BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS duplicate_check_status VARCHAR;
```

## Processing Logic

### Hash Calculation
- Uses SHA256 algorithm for better collision resistance
- Hashes the raw PDF content bytes
- Produces 64-character hexadecimal hash strings

### Duplicate Detection
- Compares PDF hashes within the same table
- Excludes the current record from duplicate checks
- Marks records as duplicates if matching hash found

### Status Updates
- Sets `duplicate_check_status` to "completed" after processing
- Updates `updated_at` timestamp
- Sets `is_duplicate` to `true` or `false` based on findings

## Error Handling

The processor handles various error scenarios and marks records appropriately:

### Error Types and Database Status Updates
- **404 Not Found**: Sets `duplicate_check_status` to `'file_not_found_404'`
- **Non-PDF Files**: Sets `duplicate_check_status` to `'non_pdf_file'`
- **Invalid URLs**: Sets `duplicate_check_status` to `'invalid_url'`
- **Network Errors**: Sets `duplicate_check_status` to `'download_failed'`
- **Other HTTP Errors**: Sets `duplicate_check_status` to `'download_failed'`

### Smart Processing
- **Skips Previously Failed Records**: Won't retry records already marked with error status
- **Detailed Statistics**: Tracks different types of failures separately
- **Graceful Continuation**: Continues processing even when individual records fail
- **Proper Logging**: Different log levels for different error types

## Monitoring and Logging

### Log Output
- Processing progress with record counts
- Download status for each PDF
- Duplicate detection results
- Error messages with details
- Final statistics summary

### Enhanced Statistics Tracked
- ✅ **Records processed successfully**: Complete hash calculation and duplicate check
- 📥 **PDFs downloaded and hashed**: Valid PDF files processed
- 🔄 **Duplicates found**: Files with matching hashes in same table
- 📄 **Non-PDF files skipped**: Files that don't have valid PDF format
- 🔗 **Invalid URLs skipped**: Malformed or empty URLs
- 🚫 **Files not found (404)**: URLs that return 404 Not Found
- 🌐 **Network errors**: Timeout, connection, or network-related failures
- ⚠️ **Other HTTP errors**: 403, 500, and other HTTP status codes
- ❌ **Other errors**: Unexpected errors during processing
- 📈 **Success rate**: Percentage of successfully processed records

## Performance Considerations

### Batch Processing
- Processes records in configurable batches
- Includes delays to avoid overwhelming servers
- Memory efficient - doesn't load all records at once

### Network Optimization
- Uses persistent HTTP sessions
- Includes proper User-Agent headers
- Implements reasonable timeouts

### Database Optimization
- Uses efficient queries with proper filtering
- Updates records individually to handle errors gracefully
- Includes proper indexing recommendations

## Recommended Database Indexes

```sql
-- Improve query performance
CREATE INDEX IF NOT EXISTS idx_bse_corp_ann_pdf_hash 
ON bse_corporate_announcements(pdf_hash) WHERE pdf_hash IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_nse_corp_ann_pdf_hash 
ON nse_corporate_announcements(pdf_hash) WHERE pdf_hash IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_bse_corp_ann_attachment_null 
ON bse_corporate_announcements(attachmentfile) WHERE attachmentfile IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_nse_corp_ann_attachment_null 
ON nse_corporate_announcements(attachment_url) WHERE attachment_url IS NOT NULL;
```

## Troubleshooting

### Common Issues

1. **Database Connection Errors**
   - Verify Supabase URL and API key
   - Check network connectivity
   - Ensure proper permissions

2. **PDF Download Failures**
   - URLs may be expired or invalid
   - Network connectivity issues
   - Server-side restrictions

3. **Memory Issues**
   - Reduce batch size with `--limit` option
   - Monitor system resources during processing

### Debug Mode
Enable detailed logging by modifying the logging level:
```python
logging.basicConfig(level=logging.DEBUG)
```

## Integration with Existing Codebase

The `pdf_hash_processor.py` integrates with the existing scraper framework:
- Uses `shared.base_config` for configuration
- Follows existing logging patterns
- Compatible with current database structure

## Future Enhancements

Potential improvements for future versions:
- Retry mechanism for failed downloads
- Parallel processing for better performance
- Progress persistence for resuming interrupted runs
- Email notifications for completion status
- Integration with existing scraper scheduling
