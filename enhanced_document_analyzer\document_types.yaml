# Document Type Configuration for AI Document Analyzer
# This file defines document types, their classification rules, and templates

document_types:
  earnings_call:
    display_name: "Earnings Call"
    keywords:
      - "earnings call"
      - "investor call"
      - "conference call"
      - "quarterly call"
    weighted_keywords:
      earnings: 2.0
      investor: 1.5
      conference: 1.5
      quarterly: 1.8
      call: 1.2
    template:
      type: "earnings_call"
      quarter: "Q1/Q2/Q3/Q4"
      financial_year: "FY2024"
      date_time: "ISO datetime"
      registration_url: "URL"
      summary: "concise description"
      confidence_score: 0.95
    validation_rules:
      required_fields:
        - "quarter"
        - "financial_year"
        - "date_time"
      field_types:
        quarter: "string"
        financial_year: "string"
        date_time: "string"
    confidence_threshold: 0.8
    priority: 10
    enabled: true

  financial_results:
    display_name: "Financial Results"
    keywords:
      - "financial results"
      - "quarterly results"
      - "annual results"
      - "profit"
      - "revenue"
    weighted_keywords:
      financial: 2.0
      results: 2.0
      profit: 1.8
      revenue: 1.8
      ebitda: 1.5
      quarterly: 1.6
      annual: 1.6
    template:
      type: "financial_results"
      quarter: "Q1/Q2/Q3/Q4"
      financial_year: "FY2024"
      revenue:
        amount: "number"
        yoy_growth: "percentage"
      ebitda:
        amount: "number"
        yoy_growth: "percentage"
      pat:
        amount: "number"
        yoy_growth: "percentage"
      management_commentary: "key insights on performance drivers"
      confidence_score: 0.90
    validation_rules:
      required_fields:
        - "quarter"
        - "financial_year"
      field_types:
        quarter: "string"
        financial_year: "string"
        revenue: "dict"
        ebitda: "dict"
        pat: "dict"
    confidence_threshold: 0.75
    priority: 9
    enabled: true

  regulatory_filing:
    display_name: "Regulatory Filing"
    keywords:
      - "regulatory filing"
      - "compliance"
      - "sebi"
      - "rbi"
      - "filing"
      - "disclosure"
    weighted_keywords:
      regulatory: 2.0
      compliance: 2.0
      sebi: 1.8
      rbi: 1.8
      filing: 1.5
      disclosure: 1.6
      regulation: 1.7
    template:
      type: "regulatory_filing"
      filing_type: "compliance/disclosure/regulatory"
      regulatory_body: "SEBI/RBI/Other"
      filing_date: "ISO date"
      compliance_requirement: "specific regulation or requirement"
      key_disclosures: "main regulatory disclosures"
      summary: "concise description of filing"
      confidence_score: 0.85
    validation_rules:
      required_fields:
        - "filing_type"
        - "regulatory_body"
      field_types:
        filing_type: "string"
        regulatory_body: "string"
        filing_date: "string"
    confidence_threshold: 0.7
    priority: 8
    enabled: true

  dividend_announcement:
    display_name: "Dividend Announcement"
    keywords:
      - "dividend"
      - "interim dividend"
      - "final dividend"
      - "dividend declaration"
    weighted_keywords:
      dividend: 2.0
      interim: 1.5
      final: 1.5
      declaration: 1.3
      payout: 1.4
    template:
      type: "dividend_announcement"
      dividend_type: "interim/final/special"
      dividend_amount: "amount per share"
      record_date: "ISO date"
      payment_date: "ISO date"
      summary: "concise description"
      confidence_score: 0.90
    validation_rules:
      required_fields:
        - "dividend_type"
        - "dividend_amount"
      field_types:
        dividend_type: "string"
        dividend_amount: "string"
        record_date: "string"
        payment_date: "string"
    confidence_threshold: 0.8
    priority: 7
    enabled: true

  merger_acquisition:
    display_name: "Merger & Acquisition"
    keywords:
      - "merger"
      - "acquisition"
      - "amalgamation"
      - "takeover"
      - "buyout"
    weighted_keywords:
      merger: 2.0
      acquisition: 2.0
      takeover: 1.8
      buyout: 1.8
      amalgamation: 1.9
      consolidation: 1.6
    template:
      type: "merger_acquisition"
      transaction_type: "merger/acquisition/takeover"
      target_company: "company name"
      transaction_value: "amount if disclosed"
      expected_completion: "timeline"
      summary: "concise description"
      confidence_score: 0.85
    validation_rules:
      required_fields:
        - "transaction_type"
      field_types:
        transaction_type: "string"
        target_company: "string"
        transaction_value: "string"
    confidence_threshold: 0.75
    priority: 6
    enabled: true

  board_meeting:
    display_name: "Board Meeting"
    keywords:
      - "board meeting"
      - "board resolution"
      - "board decision"
      - "board approval"
    weighted_keywords:
      board: 2.0
      meeting: 1.5
      resolution: 1.8
      decision: 1.6
      approval: 1.4
    template:
      type: "board_meeting"
      meeting_date: "ISO date"
      key_decisions: "main resolutions and decisions"
      summary: "concise description"
      confidence_score: 0.80
    validation_rules:
      required_fields:
        - "meeting_date"
      field_types:
        meeting_date: "string"
        key_decisions: "string"
    confidence_threshold: 0.7
    priority: 5
    enabled: true

  press_release:
    display_name: "Press Release"
    keywords:
      - "press release"
      - "announcement"
      - "update"
      - "news"
    weighted_keywords:
      press: 1.5
      announcement: 1.2
      update: 1.0
      news: 1.1
      release: 1.3
    template:
      type: "press_release"
      announcement_type: "business/operational/strategic"
      key_points: "main announcement details"
      summary: "concise description"
      confidence_score: 0.75
    validation_rules:
      required_fields: []
      field_types:
        announcement_type: "string"
        key_points: "string"
    confidence_threshold: 0.6
    priority: 3
    enabled: true

  annual_report:
    display_name: "Annual Report"
    keywords:
      - "annual report"
      - "annual general meeting"
      - "agm"
      - "yearly report"
    weighted_keywords:
      annual: 2.0
      report: 1.5
      agm: 2.0
      yearly: 1.8
      general: 1.2
      meeting: 1.4
    template:
      type: "annual_report"
      financial_year: "FY2024"
      agm_date: "ISO date"
      key_highlights: "main achievements and metrics"
      summary: "concise description"
      confidence_score: 0.85
    validation_rules:
      required_fields:
        - "financial_year"
      field_types:
        financial_year: "string"
        agm_date: "string"
    confidence_threshold: 0.75
    priority: 4
    enabled: true

  general_update:
    display_name: "General Update"
    keywords:
      - "update"
      - "information"
      - "notice"
      - "communication"
    weighted_keywords:
      update: 1.0
      information: 0.8
      notice: 1.2
      communication: 0.9
    template:
      type: "general_update"
      update_category: "operational/financial/regulatory/other"
      key_information: "main details"
      summary: "concise description"
      confidence_score: 0.60
    validation_rules:
      required_fields: []
      field_types:
        update_category: "string"
        key_information: "string"
    confidence_threshold: 0.5
    priority: 1
    enabled: true

  unknown:
    display_name: "Unknown Document Type"
    keywords: []
    weighted_keywords: {}
    template:
      type: "unknown"
      summary: "Document content summary"
      confidence_score: 0.1
      classification_notes: "Document type could not be determined with high confidence"
    validation_rules:
      required_fields: []
      field_types: {}
    confidence_threshold: 0.1
    priority: 0
    enabled: true

# Global configuration
global_settings:
  fallback_confidence_threshold: 0.2
  enable_fuzzy_matching: true
  max_keywords_per_type: 20
  enable_dynamic_loading: true
