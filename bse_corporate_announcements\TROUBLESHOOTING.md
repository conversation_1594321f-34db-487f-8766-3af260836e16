# BSE Corporate Announcements Scraper - Troubleshooting Guide

## Recent Configuration Improvements

### ✅ **Fixed Issues**

1. **Timeout Configuration Fixed**
   - **Problem**: Hardcoded 10-second timeout in session manager
   - **Solution**: Updated to use config timeout (60 seconds)
   - **File**: `session.py` line 147

2. **Enhanced Timeout Settings**
   - **REQUEST_TIMEOUT**: Increased from 30 to 60 seconds
   - **MAX_RETRIES**: Increased from 3 to 5 attempts
   - **RETRY_DELAY**: Increased from 2 to 3 seconds
   - **Added**: CONNECTION_TIMEOUT and READ_TIMEOUT settings

3. **Improved Proxy Configuration**
   - **SCRAPERAPI_RENDER_JS**: Disabled for API calls (better performance)
   - **SCRAPERAPI_PREMIUM**: Enabled for better reliability
   - **PROXY_MODE**: Changed to "endpoint" for better API compatibility

4. **Network Resilience Features**
   - Added fallback methods configuration
   - SSL verification settings
   - Redirect handling
   - User agent rotation support
   - Alternative endpoint configuration

## Current Connection Issues

### **BSE API Connectivity Problems**

The BSE API (`https://api.bseindia.com/BseIndiaAPI/api/AnnSubCategoryGetData/w`) is currently experiencing:

1. **Read Timeouts**: Even with 60-second timeout
2. **Connection Resets**: "Connection forcibly closed by remote host"
3. **Server-side Issues**: Both direct and proxy requests failing

### **Possible Causes**

1. **BSE Server Issues**
   - API server may be down or overloaded
   - Maintenance or technical problems
   - Rate limiting or blocking

2. **Network Issues**
   - ISP blocking or throttling
   - Firewall restrictions
   - DNS resolution problems

3. **Request Blocking**
   - BSE may be blocking automated requests
   - IP-based restrictions
   - User-agent filtering

## Troubleshooting Steps

### **1. Check BSE API Status**

```bash
# Test basic connectivity
curl -I "https://api.bseindia.com/BseIndiaAPI/api/AnnSubCategoryGetData/w"

# Test with parameters
curl "https://api.bseindia.com/BseIndiaAPI/api/AnnSubCategoryGetData/w?pageno=1&strCat=-1&strPrevDate=20250714&strScrip=&strSearch=P&strToDate=20250714&strType=C&subcategory=-1"
```

### **2. Test Different Connection Methods**

```bash
# Test connection only
python -m bse_corporate_announcements.main --test-connection

# Test with different timeout
python -m bse_corporate_announcements.main --timeout 120 --test-connection

# Test without proxy
python -m bse_corporate_announcements.main --no-proxy --test-connection
```

### **3. Check Network Configuration**

1. **DNS Resolution**:
   ```bash
   nslookup api.bseindia.com
   ```

2. **Ping Test**:
   ```bash
   ping api.bseindia.com
   ```

3. **Traceroute**:
   ```bash
   tracert api.bseindia.com
   ```

### **4. Alternative Solutions**

If the API continues to fail:

1. **Wait and Retry**: BSE API issues are often temporary
2. **Different Time**: Try during off-peak hours
3. **VPN**: Use a different IP/location
4. **Alternative Data Source**: Consider web scraping BSE website directly

## Configuration Options

### **Timeout Settings**

```python
# In bse_config.py
REQUEST_TIMEOUT = 60        # API request timeout
CONNECTION_TIMEOUT = 30     # Connection establishment timeout
READ_TIMEOUT = 60          # Data read timeout
MAX_RETRIES = 5            # Number of retry attempts
RETRY_DELAY = 3            # Delay between retries
```

### **Proxy Settings**

```python
# Enable/disable proxy
USE_PROXY = True

# Proxy mode
PROXY_MODE = "endpoint"     # or "proxy"

# Premium proxies for better reliability
SCRAPERAPI_PREMIUM = True
```

### **Network Resilience**

```python
# Enable fallback methods
ENABLE_FALLBACK_METHODS = True

# SSL and redirect handling
VERIFY_SSL = True
ALLOW_REDIRECTS = True
MAX_REDIRECTS = 5
```

## Monitoring and Logging

### **Enable Debug Logging**

```python
# In bse_config.py
LOG_LEVEL = "DEBUG"
```

### **Check Logs**

Look for these patterns in the output:
- `Read timed out`: Timeout issues
- `Connection forcibly closed`: Server rejection
- `Max retries exceeded`: Multiple failures
- `HTTPSConnectionPool`: Connection pool issues

## Recovery Strategies

### **Immediate Actions**

1. **Increase Timeouts**: If requests are slow but working
2. **Reduce Request Frequency**: Add delays between requests
3. **Use Premium Proxies**: Better success rates
4. **Retry Logic**: Automatic retry with exponential backoff

### **Long-term Solutions**

1. **Monitor BSE API Status**: Set up health checks
2. **Implement Circuit Breaker**: Stop requests when API is down
3. **Alternative Data Sources**: Backup scraping methods
4. **Caching**: Store and reuse recent data

## Support

If issues persist:

1. **Check BSE Website**: Verify if the main site is accessible
2. **Contact BSE**: Report API issues to BSE technical support
3. **Community Forums**: Check for similar issues reported by others
4. **Update Scraper**: Ensure you have the latest version

## Recent Changes Summary

- ✅ Fixed hardcoded timeout in session manager
- ✅ Increased timeout values for better reliability
- ✅ Enhanced proxy configuration
- ✅ Added network resilience features
- ✅ Improved error handling and retry logic
- ✅ Added alternative endpoint support
- ✅ User agent rotation capability

The configuration is now more robust and should handle temporary network issues better. However, if the BSE API server itself is having problems, these improvements won't resolve server-side issues.
