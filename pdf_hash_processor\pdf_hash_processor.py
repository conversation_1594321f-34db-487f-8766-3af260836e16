#!/usr/bin/env python3
"""
PDF Hash Processor for Corporate Announcements
Downloads PDFs from attachment URLs, calculates hashes, and updates duplicate status.

This script processes both BSE and NSE corporate announcements tables:
- bse_corporate_announcements
- nse_corporate_announcements

For each record with an attachment URL:
1. Downloads the PDF file
2. Calculates SHA256 hash of the PDF content
3. Updates the pdf_hash column
4. Checks for duplicate hashes in the same table
5. Updates is_duplicate and duplicate_check_status columns
"""

import requests
import hashlib
import json
import sys
import os
import time
from datetime import datetime
from typing import Optional, Dict, List, Tuple
import logging

# Add the parent directory to the path to import shared modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import shared configuration
from shared.base_config import BaseConfig

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('pdf_hash_processor.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class PDFHashProcessor:
    """Processes PDF hashes for corporate announcements"""
    
    def __init__(self):
        """Initialize the PDF hash processor"""
        self.config = BaseConfig()
        self.supabase_url = self.config.SUPABASE_URL
        self.supabase_key = self.config.SUPABASE_ANON_KEY
        self.headers = self._get_headers()
        self.session = self._setup_session()
        
        # Tables to process
        self.tables = {
            'bse': 'bse_corporate_announcements',
            'nse': 'nse_corporate_announcements'
        }
        
        # URL columns for each table
        self.url_columns = {
            'bse': 'attachmentfile',
            'nse': 'attachment_url'
        }
        
        # Statistics
        self.stats = {
            'processed': 0,
            'downloaded': 0,
            'hashed': 0,
            'duplicates_found': 0,
            'errors': 0
        }
    
    def _get_headers(self) -> Dict[str, str]:
        """Get headers for Supabase API requests"""
        return {
            'apikey': self.supabase_key,
            'Authorization': f'Bearer {self.supabase_key}',
            'Content-Type': 'application/json',
            'Prefer': 'return=minimal'
        }
    
    def _setup_session(self) -> requests.Session:
        """Setup requests session with proper configuration"""
        session = requests.Session()
        session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        return session
    
    def download_pdf(self, url: str) -> Optional[bytes]:
        """
        Download PDF content from URL
        
        Args:
            url (str): PDF URL to download
            
        Returns:
            Optional[bytes]: PDF content or None if failed
        """
        try:
            if not url or url.strip() == '':
                return None
            
            # Clean and validate URL
            url = url.strip()
            if not url.startswith(('http://', 'https://')):
                logger.warning(f"Invalid URL format: {url}")
                return None
            
            logger.info(f"Downloading PDF from: {url}")
            
            response = self.session.get(url, timeout=30, stream=True)
            response.raise_for_status()
            
            # Check content type
            content_type = response.headers.get('content-type', '').lower()
            if 'pdf' not in content_type and not url.lower().endswith('.pdf'):
                logger.warning(f"URL may not be a PDF: {url} (Content-Type: {content_type})")
            
            # Download content
            content = response.content
            
            # Basic PDF validation (check PDF header)
            if not content.startswith(b'%PDF'):
                logger.warning(f"Downloaded content may not be a valid PDF: {url}")
            
            logger.info(f"Successfully downloaded PDF ({len(content)} bytes)")
            return content
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Error downloading PDF from {url}: {e}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error downloading PDF from {url}: {e}")
            return None
    
    def calculate_pdf_hash(self, pdf_content: bytes) -> str:
        """
        Calculate SHA256 hash of PDF content
        
        Args:
            pdf_content (bytes): PDF file content
            
        Returns:
            str: SHA256 hash of the PDF content
        """
        try:
            hash_object = hashlib.sha256(pdf_content)
            return hash_object.hexdigest()
        except Exception as e:
            logger.error(f"Error calculating PDF hash: {e}")
            raise
    
    def get_records_to_process(self, table_name: str, limit: int = 100) -> List[Dict]:
        """
        Get records that need PDF hash processing
        
        Args:
            table_name (str): Name of the table to query
            limit (int): Maximum number of records to fetch
            
        Returns:
            List[Dict]: List of records to process
        """
        try:
            url_column = self.url_columns.get(table_name.split('_')[0])
            if not url_column:
                logger.error(f"Unknown table: {table_name}")
                return []
            
            # Query for records with attachment URLs but no PDF hash
            url = f"{self.supabase_url}/rest/v1/{table_name}"
            params = {
                'select': f'id,{url_column},pdf_hash,duplicate_check_status',
                f'{url_column}': 'not.is.null',
                'pdf_hash': 'is.null',
                'limit': str(limit)
            }
            
            response = self.session.get(url, headers=self.headers, params=params)
            response.raise_for_status()
            
            records = response.json()
            logger.info(f"Found {len(records)} records to process in {table_name}")
            return records
            
        except Exception as e:
            logger.error(f"Error fetching records from {table_name}: {e}")
            return []
    
    def update_record(self, table_name: str, record_id: str, pdf_hash: str, 
                     is_duplicate: bool) -> bool:
        """
        Update record with PDF hash and duplicate status
        
        Args:
            table_name (str): Name of the table
            record_id (str): ID of the record to update
            pdf_hash (str): Calculated PDF hash
            is_duplicate (bool): Whether this is a duplicate
            
        Returns:
            bool: True if update successful, False otherwise
        """
        try:
            url = f"{self.supabase_url}/rest/v1/{table_name}"
            params = {'id': f'eq.{record_id}'}
            
            update_data = {
                'pdf_hash': pdf_hash,
                'is_duplicate': is_duplicate,
                'duplicate_check_status': 'completed',
                'updated_at': datetime.utcnow().isoformat()
            }
            
            response = self.session.patch(
                url, 
                headers=self.headers, 
                params=params,
                data=json.dumps(update_data)
            )
            response.raise_for_status()
            
            logger.info(f"Updated record {record_id} in {table_name}")
            return True
            
        except Exception as e:
            logger.error(f"Error updating record {record_id} in {table_name}: {e}")
            return False
    
    def check_duplicate_hash(self, table_name: str, pdf_hash: str, 
                           current_record_id: str) -> bool:
        """
        Check if PDF hash already exists in the table
        
        Args:
            table_name (str): Name of the table
            pdf_hash (str): PDF hash to check
            current_record_id (str): ID of current record (to exclude from check)
            
        Returns:
            bool: True if duplicate found, False otherwise
        """
        try:
            url = f"{self.supabase_url}/rest/v1/{table_name}"
            params = {
                'select': 'id',
                'pdf_hash': f'eq.{pdf_hash}',
                'id': f'neq.{current_record_id}'
            }
            
            response = self.session.get(url, headers=self.headers, params=params)
            response.raise_for_status()
            
            duplicates = response.json()
            return len(duplicates) > 0
            
        except Exception as e:
            logger.error(f"Error checking for duplicate hash in {table_name}: {e}")
            return False

    def process_record(self, table_name: str, record: Dict) -> bool:
        """
        Process a single record: download PDF, calculate hash, update database

        Args:
            table_name (str): Name of the table
            record (Dict): Record data

        Returns:
            bool: True if processing successful, False otherwise
        """
        try:
            record_id = record['id']
            url_column = self.url_columns.get(table_name.split('_')[0])
            attachment_url = record.get(url_column)

            if not attachment_url:
                logger.warning(f"No attachment URL for record {record_id}")
                return False

            logger.info(f"Processing record {record_id} from {table_name}")

            # Download PDF
            pdf_content = self.download_pdf(attachment_url)
            if not pdf_content:
                self.stats['errors'] += 1
                return False

            self.stats['downloaded'] += 1

            # Calculate hash
            pdf_hash = self.calculate_pdf_hash(pdf_content)
            self.stats['hashed'] += 1

            # Check for duplicates
            is_duplicate = self.check_duplicate_hash(table_name, pdf_hash, record_id)
            if is_duplicate:
                self.stats['duplicates_found'] += 1
                logger.info(f"Duplicate PDF hash found for record {record_id}")

            # Update database
            success = self.update_record(table_name, record_id, pdf_hash, is_duplicate)
            if success:
                self.stats['processed'] += 1
                return True
            else:
                self.stats['errors'] += 1
                return False

        except Exception as e:
            logger.error(f"Error processing record {record.get('id', 'unknown')}: {e}")
            self.stats['errors'] += 1
            return False

    def process_table(self, table_name: str, batch_size: int = 50) -> None:
        """
        Process all records in a table

        Args:
            table_name (str): Name of the table to process
            batch_size (int): Number of records to process in each batch
        """
        logger.info(f"Starting processing for table: {table_name}")

        total_processed = 0

        while True:
            # Get batch of records to process
            records = self.get_records_to_process(table_name, batch_size)

            if not records:
                logger.info(f"No more records to process in {table_name}")
                break

            logger.info(f"Processing batch of {len(records)} records from {table_name}")

            # Process each record in the batch
            for record in records:
                try:
                    self.process_record(table_name, record)
                    total_processed += 1

                    # Add small delay to avoid overwhelming the server
                    time.sleep(0.1)

                except Exception as e:
                    logger.error(f"Error processing record: {e}")
                    continue

            logger.info(f"Completed batch. Total processed so far: {total_processed}")

            # Add delay between batches
            time.sleep(1)

        logger.info(f"Completed processing {table_name}. Total records processed: {total_processed}")

    def process_all_tables(self, batch_size: int = 50) -> None:
        """
        Process all corporate announcements tables

        Args:
            batch_size (int): Number of records to process in each batch
        """
        logger.info("Starting PDF hash processing for all tables")

        for exchange, table_name in self.tables.items():
            try:
                logger.info(f"Processing {exchange.upper()} table: {table_name}")
                self.process_table(table_name, batch_size)
            except Exception as e:
                logger.error(f"Error processing table {table_name}: {e}")
                continue

        self.print_statistics()

    def print_statistics(self) -> None:
        """Print processing statistics"""
        logger.info("=== PDF Hash Processing Statistics ===")
        logger.info(f"Records processed: {self.stats['processed']}")
        logger.info(f"PDFs downloaded: {self.stats['downloaded']}")
        logger.info(f"Hashes calculated: {self.stats['hashed']}")
        logger.info(f"Duplicates found: {self.stats['duplicates_found']}")
        logger.info(f"Errors encountered: {self.stats['errors']}")
        logger.info("=====================================")


def main():
    """Main function to run the PDF hash processor"""
    try:
        logger.info("Starting PDF Hash Processor")

        processor = PDFHashProcessor()

        # Process all tables with batch size of 50
        processor.process_all_tables(batch_size=50)

        logger.info("PDF Hash Processor completed successfully")

    except KeyboardInterrupt:
        logger.info("Processing interrupted by user")
    except Exception as e:
        logger.error(f"Fatal error in PDF Hash Processor: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
