#!/usr/bin/env python3
"""
Quick Process Script - Generate summaries for all unprocessed records

This is a simplified script for the most common use case:
processing all records that don't have summaries yet.

Usage:
    python quick_process.py                    # Process all unprocessed records
    python quick_process.py --preview          # Show what would be processed
    python quick_process.py --batch-size 25   # Use custom batch size
    python quick_process.py --resume          # Resume interrupted processing
"""

import argparse
import sys
from datetime import datetime
from ai_document_analyzer import DocumentAnalyzer, DEFAULT_CONFIG

def main():
    parser = argparse.ArgumentParser(
        description="Quick processing of all unprocessed records",
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    
    parser.add_argument('--preview', action='store_true',
                       help='Show what would be processed without doing it')
    parser.add_argument('--batch-size', type=int, default=50,
                       help='Records per batch (default: 50)')
    parser.add_argument('--resume', action='store_true',
                       help='Resume from previous session')
    
    args = parser.parse_args()
    
    print("🚀 Quick Process - Enhanced Document Analyzer")
    print("=" * 50)
    
    # Initialize analyzer
    try:
        analyzer = DocumentAnalyzer(DEFAULT_CONFIG)
    except Exception as e:
        print(f"❌ Error initializing: {str(e)}")
        sys.exit(1)
    
    # Get status
    total_records = analyzer.get_all_record_count()
    unprocessed_records = analyzer.get_unprocessed_count()
    
    print(f"📊 Database Status:")
    print(f"   Total records: {total_records:,}")
    print(f"   Already have summaries: {total_records - unprocessed_records:,}")
    print(f"   Missing summaries (NULL): {unprocessed_records:,}")
    print(f"   Batch size: {args.batch_size}")
    print(f"   Will process: ONLY records with NULL summary")

    if unprocessed_records == 0:
        print("✅ All records already have summaries!")
        return
    
    if args.preview:
        batches = (unprocessed_records + args.batch_size - 1) // args.batch_size
        print(f"\n🎯 Would process {unprocessed_records:,} records in {batches} batches")
        print("✅ Preview complete - use without --preview to start processing")
        return
    
    # Confirm processing
    if unprocessed_records > 50:
        print(f"\n⚠️  About to process {unprocessed_records:,} records")
        confirm = input("Continue? (yes/no): ").lower().strip()
        if confirm != 'yes':
            print("Cancelled")
            return
    
    print(f"\n🚀 Starting processing at {datetime.now().strftime('%H:%M:%S')}")
    print("   Press Ctrl+C to stop (progress will be saved)")
    
    try:
        # Process all unprocessed records
        final_stats = analyzer.process_all_records(
            batch_size=args.batch_size,
            skip_existing=True,  # Only process unprocessed
            save_progress=True,
            progress_file='processing_progress.json'
        )
        
        print(f"\n🎉 COMPLETED at {datetime.now().strftime('%H:%M:%S')}")
        print(f"✅ Processed: {final_stats['processed']:,}")
        print(f"❌ Failed: {final_stats['failed']:,}")
        print(f"📊 Success rate: {final_stats['success_rate']:.1f}%")
        
    except KeyboardInterrupt:
        print(f"\n⏸️  Stopped by user at {datetime.now().strftime('%H:%M:%S')}")
        print("   Use --resume to continue later")
    except Exception as e:
        print(f"\n❌ Error: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
