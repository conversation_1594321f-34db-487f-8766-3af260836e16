# 🚀 Enhanced Document Analyzer - Command Summary

## 📋 Quick Reference

### 🎯 Most Common Commands

```bash
# 1. QUICK START - Process ONLY records with NULL summary
python quick_process.py

# 2. PREVIEW - See what needs processing (NULL summaries only)
python quick_process.py --preview

# 3. DETAILED PREVIEW - Full database status
python generate_all_summaries.py --preview

# 4. PROCESS SINGLE RECORD - Test with one record
python ai_document_analyzer.py --ids YOUR_RECORD_ID

# 5. VERIFY NULL FILTERING - Test that only NULL summaries are processed
python test_null_filtering.py

# 6. TEST OPENAI INTEGRATION - Verify OpenAI API is working
python test_openai_integration.py
```

## 🔧 All Available Scripts

### 1. `quick_process.py` - Simplified Processing
```bash
python quick_process.py                    # Process all unprocessed
python quick_process.py --preview          # Show preview
python quick_process.py --batch-size 25    # Custom batch size
python quick_process.py --resume           # Resume interrupted
```

### 2. `generate_all_summaries.py` - Full-Featured Batch Processing
```bash
python generate_all_summaries.py --preview                    # Database status
python generate_all_summaries.py --process-unprocessed-only   # Recommended
python generate_all_summaries.py --process-all                # All records
python generate_all_summaries.py --resume                     # Resume
python generate_all_summaries.py --status                     # Check progress
```

### 3. `ai_document_analyzer.py` - Core Analyzer
```bash
# Single records
python ai_document_analyzer.py --ids RECORD_ID
python ai_document_analyzer.py --ids "id1,id2,id3"

# Batch processing
python ai_document_analyzer.py --batch-process --limit 100
python ai_document_analyzer.py --process-all --limit 50

# Advanced options
python ai_document_analyzer.py --batch-process --skip-existing
python ai_document_analyzer.py --batch-process --force
python ai_document_analyzer.py --batch-process --resume-from RECORD_ID
python ai_document_analyzer.py --batch-process --save-progress
```

## 🎯 Recommended Workflow

### First Time Setup
1. **Test single record**: `python ai_document_analyzer.py --ids 180ecf34-12c8-4d41-a6a6-89705a0551a0`
2. **Check database status**: `python generate_all_summaries.py --preview`
3. **Start processing**: `python quick_process.py`

### Regular Processing
1. **Quick check**: `python quick_process.py --preview`
2. **Process new records**: `python quick_process.py`

### If Interrupted
1. **Check status**: `python generate_all_summaries.py --status`
2. **Resume**: `python quick_process.py --resume`

## ⚙️ Command Options Explained

### Batch Size Options
- `--batch-size 10` - Small batches (safer, slower)
- `--batch-size 50` - Default (balanced)
- `--batch-size 100` - Large batches (faster, more resources)

### Processing Modes
- `--process-unprocessed-only` - Only records without summaries (recommended)
- `--process-all` - All records including already processed (use with caution)
- `--skip-existing` - Skip records that have summaries
- `--force` - Reprocess records even if they have summaries

### Progress & Resume
- `--save-progress` - Save progress for resuming (automatic in batch scripts)
- `--resume` - Continue from where processing stopped
- `--resume-from RECORD_ID` - Start from specific record
- `--status` - Show current progress

### Testing & Preview
- `--preview` - Show what would be processed
- `--dry-run` - Test run without actual processing
- `--log-level DEBUG` - Detailed logging for troubleshooting

## 📊 Current Database Status

Based on the last check:
- **Total records**: 29
- **Records with summaries**: 3
- **Records with NULL summary**: 26
- **Completion rate**: 10.3%

## ✅ NULL Summary Filtering Verified

The system has been tested and confirmed to ONLY process records where `summary IS NULL`:
- ✅ Filtering logic working correctly
- ✅ Verification system in place
- ✅ No records with existing summaries will be processed (unless forced)
- ✅ All default commands target NULL summaries only

## 🚨 Important Notes

### Before Large Batch Processing
1. **Test first**: Process a single record to ensure everything works
2. **Check resources**: Ensure adequate memory and stable internet
3. **Backup consideration**: For large databases, consider backing up first

### During Processing
- **Monitor progress**: Check logs or use status commands
- **Don't interrupt unnecessarily**: Progress is saved automatically
- **Resource monitoring**: Watch system resources during large batches

### Error Handling
- **Automatic retry**: Failed records are retried with fallback methods
- **Progress preservation**: Interruptions don't lose progress
- **Detailed logging**: All errors are logged with context

## 🔍 Monitoring Commands

```bash
# Check current progress
python generate_all_summaries.py --status

# View latest log file (Windows)
type ai_analysis_*.log | findstr /i error

# Monitor processing (if available)
Get-Content ai_analysis_*.log -Wait -Tail 10
```

## 📈 Performance Tips

1. **Start small**: Use `--batch-size 25` for first runs
2. **Monitor success rate**: Aim for >90% success rate
3. **Stable connection**: Ensure reliable internet for API calls
4. **Off-peak processing**: Run during low-traffic hours
5. **Resume capability**: Use progress saving for long runs

## 🎉 Success Indicators

- ✅ High success rate (>90%)
- ✅ Consistent processing speed
- ✅ Low error count in logs
- ✅ Progress saving working
- ✅ Database updates successful

## 📞 Troubleshooting

### Common Issues
1. **Import errors**: Run `python setup.py` to verify installation
2. **Database connection**: Check Supabase credentials
3. **API limits**: Reduce batch size if hitting rate limits
4. **Memory issues**: Reduce batch size or restart system

### Getting Help
1. Check log files for detailed error messages
2. Use `--dry-run` to test configuration
3. Try processing a single record first
4. Verify all dependencies are installed

---

**Ready to process all your records!** 🚀

Start with: `python quick_process.py --preview`
