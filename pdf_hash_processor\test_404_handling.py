#!/usr/bin/env python3
"""
Test script to verify 404 error handling in PDF processor
"""

import requests
import logging
from pdf_hash_standalone import PDFHashProcessor

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_404_handling():
    """Test how the processor handles 404 errors"""
    
    processor = PDFHashProcessor()
    
    # Test URLs - some that should work, some that should return 404
    test_urls = [
        # This should return 404 (the one from your log)
        "https://www.bseindia.com/xml-data/corpfiling/AttachLive/50d020dd-284f-4e07-953f-ebbf1ea93da6.pdf",
        
        # This should also return 404
        "https://www.bseindia.com/xml-data/corpfiling/AttachLive/be2f1bf1-015d-47a9-af79-7a032be3a462.pdf",
        
        # Invalid URL format
        "not-a-valid-url",
        
        # Non-existent domain
        "https://nonexistent-domain-12345.com/test.pdf"
    ]
    
    print("Testing 404 and error handling...")
    print("=" * 50)
    
    for i, url in enumerate(test_urls, 1):
        print(f"\nTest {i}: {url}")
        print("-" * 40)
        
        result = processor.download_pdf(url)
        
        if result and result[0] is not None:
            content, error_type = result
            print(f"✅ Success: Downloaded {len(content)} bytes")
        else:
            error_type = result[1] if result else 'unknown_error'
            print(f"❌ Failed: {error_type}")
    
    print("\n" + "=" * 50)
    print("Final Statistics:")
    processor.print_stats()

def test_small_batch():
    """Test processing a small batch of real records"""
    print("\nTesting small batch processing...")
    print("=" * 50)
    
    processor = PDFHashProcessor()
    
    # Process just 5 BSE records to see the error handling in action
    try:
        processor.process_table('bse', batch_size=5)
        print("\nBatch processing completed!")
        processor.print_stats()
    except Exception as e:
        print(f"Error during batch processing: {e}")

def main():
    """Run tests"""
    print("PDF Hash Processor - 404 Error Handling Test")
    print("=" * 60)
    
    # Test 1: Individual URL testing
    test_404_handling()
    
    # Test 2: Small batch processing (commented out to avoid accidental DB updates)
    print("\n" + "=" * 60)
    print("To test batch processing with real data, uncomment the line below:")
    print("# test_small_batch()")
    
    print("\n✅ Error handling tests completed!")

if __name__ == "__main__":
    main()
