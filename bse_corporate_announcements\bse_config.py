"""
Dedicated configuration for BSE Corporate Announcements Scraper
This is a standalone configuration that doesn't depend on shared modules.
"""

import os
from datetime import datetime


class BSECorporateAnnouncementsConfig:
    """
    Standalone configuration class for BSE Corporate Announcements scraper.
    Contains all settings specific to this feature without external dependencies.
    """

    def __init__(self):
        """Initialize BSE Corporate Announcements configuration"""
        
        # ===== CORE BSE SETTINGS =====
        self.BASE_URL = "https://www.bseindia.com"
        self.API_BASE_URL = "https://api.bseindia.com"
        self.EXCHANGE_NAME = "BSE Corporate Announcements"
        
        # ===== API ENDPOINTS =====
        self.CORPORATE_ANNOUNCEMENTS_API_URL = "/BseIndiaAPI/api/AnnSubCategoryGetData/w"
        
        # ===== DATABASE SETTINGS =====
        # Supabase configuration
        self.SUPABASE_URL = "https://yvuwseolouiqhoxsieop.supabase.co"
        self.SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl2dXdzZW9sb3VpcWhveHNpZW9wIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI1OTI2ODcsImV4cCI6MjA2ODE2ODY4N30.W7m701xSGJ5eh8oc4turmb4zO9-nz1Pbzqz-DL9EEow"
        self.DATABASE_TABLE = "bse_corporate_announcements"
        
        # ===== FILE STORAGE SETTINGS =====
        self.OUTPUT_FOLDER = "bse_corporate_announcements_data"
        self.CSV_FILENAME_PREFIX = "bse_corporate_announcements"
        self.HTML_FILENAME_PREFIX = "bse_corporate_announcements_response"
        
        # ===== DATE FORMAT SETTINGS =====
        self.BSE_DATE_FORMAT = "%Y%m%d"  # Format used in API: 20250714
        self.DISPLAY_DATE_FORMAT = "%d/%m/%Y"  # Format for display: 14/07/2025
        self.FILENAME_DATE_FORMAT = "%Y%m%d_%H%M%S"
        
        # ===== API DEFAULT PARAMETERS =====
        self.DEFAULT_CATEGORY = "-1"  # All categories
        self.DEFAULT_SUBCATEGORY = "-1"  # All subcategories
        self.DEFAULT_TYPE = "C"  # Corporate announcements
        self.DEFAULT_SEARCH = "P"  # Search parameter
        self.DEFAULT_SCRIP = ""  # Empty for all scrips
        self.DEFAULT_PAGE_SIZE = 100  # Records per page
        
        # ===== REQUEST SETTINGS =====
        self.REQUEST_TIMEOUT = 60  # Increased timeout for BSE API
        self.MAX_RETRIES = 5  # More retries for better reliability
        self.RETRY_DELAY = 3  # Longer delay between retries
        self.CONNECTION_TIMEOUT = 30  # Connection timeout
        self.READ_TIMEOUT = 60  # Read timeout
        
        # ===== SCRAPING SETTINGS =====
        self.DEFAULT_DAYS_BACK = 7
        
        # ===== FILE SETTINGS =====
        self.SAVE_CSV = True
        self.USE_DATABASE = True
        self.CSV_EXTENSION = ".csv"
        self.HTML_EXTENSION = ".html"
        
        # ===== PROXY SETTINGS =====
        # ScraperAPI configuration
        self.USE_PROXY = True
        self.SCRAPERAPI_KEY = "********************************"
        self.SCRAPERAPI_ENDPOINT = "http://api.scraperapi.com"
        self.SCRAPERAPI_PROXY_HOST = "proxy-server.scraperapi.com"
        self.SCRAPERAPI_PROXY_PORT = 8001
        self.SCRAPERAPI_USERNAME = "scraperapi"

        # ScraperAPI parameters
        self.SCRAPERAPI_RENDER_JS = False  # Disable JS rendering for API calls
        self.SCRAPERAPI_COUNTRY_CODE = "IN"
        self.SCRAPERAPI_PREMIUM = True  # Use premium proxies for better reliability
        self.PROXY_MODE = "endpoint"  # Use endpoint mode for better API compatibility
        
        # ===== LOGGING SETTINGS =====
        self.LOG_LEVEL = "INFO"
        self.LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

        # ===== NETWORK RESILIENCE SETTINGS =====
        self.ENABLE_FALLBACK_METHODS = True  # Try multiple connection methods
        self.VERIFY_SSL = True  # SSL verification
        self.ALLOW_REDIRECTS = True  # Follow redirects
        self.MAX_REDIRECTS = 5  # Maximum redirects to follow

        # ===== ALTERNATIVE API ENDPOINTS =====
        # In case the main API is down, we can try alternative approaches
        self.ALTERNATIVE_ENDPOINTS = [
            "/BseIndiaAPI/api/AnnSubCategoryGetData/w",  # Primary endpoint
            "/corporates/ann.aspx",  # Alternative web endpoint
        ]

        # ===== USER AGENT ROTATION =====
        self.USER_AGENTS = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/119.0",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
        ]
        
        # ===== VALIDATION =====
        self._validate_config()

    def _validate_config(self):
        """Validate configuration settings"""
        if self.REQUEST_TIMEOUT <= 0:
            raise ValueError("REQUEST_TIMEOUT must be positive")
        
        if self.DEFAULT_DAYS_BACK <= 0:
            raise ValueError("DEFAULT_DAYS_BACK must be positive")
        
        if not self.SUPABASE_URL or not self.SUPABASE_ANON_KEY:
            raise ValueError("Supabase URL and key must be provided")

    def get_output_path(self, filename):
        """
        Get full output path for a file.
        
        Args:
            filename (str): Name of the file
            
        Returns:
            str: Full path to the file
        """
        return os.path.join(self.OUTPUT_FOLDER, filename)

    def get_database_headers(self):
        """
        Get headers for database API requests.
        
        Returns:
            dict: Headers for Supabase API requests
        """
        return {
            'apikey': self.SUPABASE_ANON_KEY,
            'Authorization': f'Bearer {self.SUPABASE_ANON_KEY}',
            'Content-Type': 'application/json',
            'Prefer': 'return=minimal'
        }

    def get_database_url(self, endpoint=""):
        """
        Get full database URL for API requests.

        Args:
            endpoint (str): API endpoint to append

        Returns:
            str: Full database URL
        """
        base_url = f"{self.SUPABASE_URL}/rest/v1/{self.DATABASE_TABLE}"
        if endpoint:
            return f"{base_url}/{endpoint}"
        return base_url

    def get_scraperapi_url(self, target_url):
        """
        Get ScraperAPI endpoint URL with target URL and parameters.

        Args:
            target_url (str): The URL to scrape through ScraperAPI

        Returns:
            str: Complete ScraperAPI URL with parameters
        """
        if not self.USE_PROXY:
            return target_url

        params = [
            f"api_key={self.SCRAPERAPI_KEY}",
            f"url={target_url}"
        ]

        if self.SCRAPERAPI_RENDER_JS:
            params.append("render=true")

        if self.SCRAPERAPI_COUNTRY_CODE:
            params.append(f"country_code={self.SCRAPERAPI_COUNTRY_CODE}")

        if self.SCRAPERAPI_PREMIUM:
            params.append("premium=true")

        return f"{self.SCRAPERAPI_ENDPOINT}?{'&'.join(params)}"

    def get_proxy_config(self):
        """
        Get proxy configuration for requests session.

        Returns:
            dict: Proxy configuration or None if proxy is disabled
        """
        if not self.USE_PROXY or self.PROXY_MODE == 'endpoint':
            return None

        # Build username with parameters for proxy mode
        username_parts = [self.SCRAPERAPI_USERNAME]

        if self.SCRAPERAPI_RENDER_JS:
            username_parts.append("render=true")

        if self.SCRAPERAPI_COUNTRY_CODE:
            username_parts.append(f"country_code={self.SCRAPERAPI_COUNTRY_CODE}")

        if self.SCRAPERAPI_PREMIUM:
            username_parts.append("premium=true")

        username = ".".join(username_parts)
        proxy_url = f"http://{username}:{self.SCRAPERAPI_KEY}@{self.SCRAPERAPI_PROXY_HOST}:{self.SCRAPERAPI_PROXY_PORT}"

        return {
            'http': proxy_url,
            'https': proxy_url
        }

    def should_use_endpoint_mode(self):
        """
        Check if we should use ScraperAPI endpoint mode instead of proxy mode.

        Returns:
            bool: True if endpoint mode should be used
        """
        return self.USE_PROXY and self.PROXY_MODE == 'endpoint'

    def get_api_url(self):
        """
        Get the complete API URL for corporate announcements.
        
        Returns:
            str: Complete API URL
        """
        return f"{self.API_BASE_URL}{self.CORPORATE_ANNOUNCEMENTS_API_URL}"

    def format_date_for_api(self, date_obj):
        """
        Format date for BSE API.
        
        Args:
            date_obj (datetime): Date object to format
            
        Returns:
            str: Formatted date string
        """
        return date_obj.strftime(self.BSE_DATE_FORMAT)

    def format_date_for_display(self, date_obj):
        """
        Format date for display.
        
        Args:
            date_obj (datetime): Date object to format
            
        Returns:
            str: Formatted date string
        """
        return date_obj.strftime(self.DISPLAY_DATE_FORMAT)

    def get_filename_timestamp(self):
        """
        Get current timestamp for filename.
        
        Returns:
            str: Formatted timestamp
        """
        return datetime.now().strftime(self.FILENAME_DATE_FORMAT)

    def __str__(self):
        """String representation of configuration"""
        return f"BSECorporateAnnouncementsConfig(exchange={self.EXCHANGE_NAME}, table={self.DATABASE_TABLE})"

    def __repr__(self):
        """Detailed representation of configuration"""
        return (f"BSECorporateAnnouncementsConfig("
                f"base_url={self.BASE_URL}, "
                f"api_url={self.API_BASE_URL}, "
                f"table={self.DATABASE_TABLE}, "
                f"output_folder={self.OUTPUT_FOLDER})")


# Create the configuration instance
bse_config = BSECorporateAnnouncementsConfig()

# Export commonly used settings for backward compatibility
BASE_URL = bse_config.BASE_URL
API_BASE_URL = bse_config.API_BASE_URL
CORPORATE_ANNOUNCEMENTS_API_URL = bse_config.CORPORATE_ANNOUNCEMENTS_API_URL
SUPABASE_URL = bse_config.SUPABASE_URL
SUPABASE_ANON_KEY = bse_config.SUPABASE_ANON_KEY
DATABASE_TABLE = bse_config.DATABASE_TABLE
OUTPUT_FOLDER = bse_config.OUTPUT_FOLDER
DEFAULT_DAYS_BACK = bse_config.DEFAULT_DAYS_BACK
REQUEST_TIMEOUT = bse_config.REQUEST_TIMEOUT
SAVE_CSV = bse_config.SAVE_CSV
USE_DATABASE = bse_config.USE_DATABASE
